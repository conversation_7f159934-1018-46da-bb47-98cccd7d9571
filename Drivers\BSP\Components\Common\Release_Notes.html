<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40"><head>











  
  <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">

  
  <link rel="File-List" href="Library_files/filelist.xml">

  
  <link rel="Edit-Time-Data" href="Library_files/editdata.mso"><!--[if !mso]> <style> v\:* {behavior:url(#default#VML);} o\:* {behavior:url(#default#VML);} w\:* {behavior:url(#default#VML);} .shape {behavior:url(#default#VML);} </style> <![endif]--><title>Release Notes for BSP Components Common Drivers</title><!--[if gte mso 9]><xml> <o:DocumentProperties> <o:Author>STMicroelectronics</o:Author> <o:LastAuthor>STMicroelectronics</o:LastAuthor> <o:Revision>37</o:Revision> <o:TotalTime>136</o:TotalTime> <o:Created>2009-02-27T19:26:00Z</o:Created> <o:LastSaved>2009-03-01T17:56:00Z</o:LastSaved> <o:Pages>1</o:Pages> <o:Words>522</o:Words> <o:Characters>2977</o:Characters> <o:Company>STMicroelectronics</o:Company> <o:Lines>24</o:Lines> <o:Paragraphs>6</o:Paragraphs> <o:CharactersWithSpaces>3493</o:CharactersWithSpaces> <o:Version>11.6568</o:Version> </o:DocumentProperties> </xml><![endif]--><!--[if gte mso 9]><xml> <w:WordDocument> <w:Zoom>110</w:Zoom> <w:ValidateAgainstSchemas/> <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid> <w:IgnoreMixedContent>false</w:IgnoreMixedContent> <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText> <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel> </w:WordDocument> </xml><![endif]--><!--[if gte mso 9]><xml> <w:LatentStyles DefLockedState="false" LatentStyleCount="156"> </w:LatentStyles> </xml><![endif]-->


  

  

  
  <style>
<!--
/* Style Definitions */
p.MsoNormal, li.MsoNormal, div.MsoNormal
{mso-style-parent:"";
margin:0in;
margin-bottom:.0001pt;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
h2
{mso-style-next:Normal;
margin-top:12.0pt;
margin-right:0in;
margin-bottom:3.0pt;
margin-left:0in;
mso-pagination:widow-orphan;
page-break-after:avoid;
mso-outline-level:2;
font-size:14.0pt;
font-family:Arial;
font-weight:bold;
font-style:italic;}
a:link, span.MsoHyperlink
{color:blue;
text-decoration:underline;
text-underline:single;}
a:visited, span.MsoHyperlinkFollowed
{color:blue;
text-decoration:underline;
text-underline:single;}
p
{mso-margin-top-alt:auto;
margin-right:0in;
mso-margin-bottom-alt:auto;
margin-left:0in;
mso-pagination:widow-orphan;
font-size:12.0pt;
font-family:"Times New Roman";
mso-fareast-font-family:"Times New Roman";}
@page Section1
{size:8.5in 11.0in;
margin:1.0in 1.25in 1.0in 1.25in;
mso-header-margin:.5in;
mso-footer-margin:.5in;
mso-paper-source:0;}
div.Section1
{page:Section1;}
-->
  </style><!--[if gte mso 10]> <style> /* Style Definitions */ table.MsoNormalTable {mso-style-name:"Table Normal"; mso-tstyle-rowband-size:0; mso-tstyle-colband-size:0; mso-style-noshow:yes; mso-style-parent:""; mso-padding-alt:0in 5.4pt 0in 5.4pt; mso-para-margin:0in; mso-para-margin-bottom:.0001pt; mso-pagination:widow-orphan; font-size:10.0pt; font-family:"Times New Roman"; mso-ansi-language:#0400; mso-fareast-language:#0400; mso-bidi-language:#0400;} </style> <![endif]--><!--[if gte mso 9]><xml> <o:shapedefaults v:ext="edit" spidmax="5122"/> </xml><![endif]--><!--[if gte mso 9]><xml> <o:shapelayout v:ext="edit"> <o:idmap v:ext="edit" data="1"/> </o:shapelayout></xml><![endif]-->
  <meta content="MCD Application Team" name="author"></head>
<body link="blue" vlink="blue">
<div class="Section1">
<p class="MsoNormal"><span style="font-family: Arial;"><o:p><br>
</o:p></span></p>
<div align="center">
<table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
  <tbody>
    <tr>
      <td style="padding: 0cm;" valign="top">
      <table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" cellspacing="0" width="900">
        <tbody>
          <tr>
            <td style="vertical-align: top;">
            <p class="MsoNormal"><span style="font-size: 8pt; font-family: Arial; color: blue;"><a href="../../../../Release_Notes.html">Back to Release page</a><o:p></o:p></span></p>
            </td>
          </tr>
          <tr style="">
            <td style="padding: 1.5pt;">
            
            <h1 style="margin-bottom: 18pt; text-align: center;" align="center"><span style="font-size: 20pt; font-family: Verdana; color: rgb(51, 102, 255);">Release
Notes for BSP Components Common&nbsp; Drivers</span><span style="font-size: 20pt; font-family: Verdana;"><o:p></o:p></span></h1>

            <p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;">Copyright
2015 STMicroelectronics</span><span style="color: black;"><u1:p></u1:p><o:p></o:p></span></p>
            <p class="MsoNormal" style="text-align: center;" align="center"><span style="font-size: 10pt; font-family: Arial; color: black;"><img alt="" id="_x0000_i1025" src="../../../../_htmresc/st_logo.png" style="border: 0px solid ; width: 86px; height: 65px;"></span><span style="font-size: 10pt;"><o:p></o:p></span></p>
            </td>
          </tr>
        </tbody>
      </table>
      <p class="MsoNormal"><span style="font-family: Arial; display: none;"><o:p>&nbsp;</o:p></span></p>
      <table class="MsoNormalTable" style="width: 675pt;" border="0" cellpadding="0" width="900">
        <tbody>
          <tr style="">
            <td style="padding: 0cm;" valign="top">
            <span style="font-family: &quot;Times New Roman&quot;;">
            </span>
            <h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial;"><a name="History"></a><span style="font-size: 12pt; color: white;">Update History</span></h2>
            <h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 210px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V4.0.1 / 21-July-2015 <o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>









            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>





            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>




            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>

            <span style="font-size: 10pt; font-family: Verdana;"></span>
            


            <span style="font-size: 10pt; font-family: Verdana;"></span><ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">tsensor.h: Fix compilation issue on TSENSOR_InitTypeDef</span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 210px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V4.0.0 / 22-June-2015 <o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>









            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>





            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>




            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>

            <span style="font-size: 10pt; font-family: Verdana;"></span>
            


            <span style="font-size: 10pt; font-family: Verdana;"></span><ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;">accelero.h: add <span style="font-style: italic;">*DeInit</span> field in <span style="font-style: italic;">ACCELERO_DrvTypeDef</span> structure</span></li><li><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">audio.h: add <span style="font-style: italic;">*DeInit</span> field in <span style="font-style: italic;">AUDIO_DrvTypeDef</span> structure</span></li><li><span style="font-size: 10pt; font-family: Verdana;">idd.h:&nbsp;</span></li><ul><li><span style="font-size: 10pt; font-family: Verdana;">add <span style="font-style: italic;">Shunt0StabDelay, Shunt1StabDelay, Shunt2StabDelay, Shunt3StabDelay, Shunt4StabDelay and ShuntNbOnBoard </span></span><span style="font-size: 10pt; font-family: Verdana;">fields in <span style="font-style: italic;">IDD_ConfigTypeDef</span>&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">structure</span></li><li><span style="font-size: 10pt; font-family: Verdana;">rename <span style="font-style: italic;">ShuntNumber</span> field to <span style="font-style: italic;">ShuntNbUsed</span> in </span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">IDD_ConfigTypeDef</span> structure</span></li></ul><li><span style="font-size: 10pt; font-family: Verdana;">magneto.h: add <span style="font-style: italic;">*DeInit</span> field in <span style="font-style: italic;">MAGNETO_DrvTypeDef</span> structure</span></li><li><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">Important Note:</span>&nbsp; this release V4.0.0 is not backward compatible with V3.0.0</span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 210px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V3.0.0 / 28-April-2015 <o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>









            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>





            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>




            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>

            <span style="font-size: 10pt; font-family: Verdana;"></span>
            


            <span style="font-size: 10pt; font-family: Verdana;"></span><ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">accelero.h:&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">add <span style="font-style: italic;">*LowPower</span> field in <span style="font-style: italic;">ACCELERO_DrvTypeDef</span> structure</span><span style="font-size: 10pt; font-family: Verdana;"></span></li><li><span style="font-size: 10pt; font-family: Verdana;">magneto.h:&nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">add <span style="font-style: italic;">*LowPower</span> field in <span style="font-style: italic;">MAGNETO_DrvTypeDef</span> structure</span></li><li><span style="font-size: 10pt; font-family: Verdana;">gyro.h: add <span style="font-style: italic;">*DeInit</span> and <span style="font-style: italic;">*LowPower</span> fields in <span style="font-style: italic;">GYRO_DrvTypeDef</span> structure<br></span></li><li><span style="font-size: 10pt; font-family: Verdana;">camera.h: add <span style="font-style: italic;">CAMERA_COLOR_EFFECT_NONE</span> define</span></li><li><span style="font-size: 10pt; font-family: Verdana;">idd.h:&nbsp;</span></li><ul><li><span style="font-size: 10pt; font-family: Verdana;">add <span style="font-style: italic;">MeasureNb</span>, <span style="font-style: italic;">DeltaDelayUnit</span> and <span style="font-style: italic;">DeltaDelayValue</span> fields in <span style="font-style: italic;">IDD_ConfigTypeDef</span> structure</span></li><li><span style="font-size: 10pt; font-family: Verdana;">rename <span style="font-style: italic;">PreDelay</span> field to <span style="font-style: italic;">PreDelayUnit</span> in </span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic;">IDD_ConfigTypeDef</span> structure</span></li></ul>
              <li><span style="font-size: 10pt; font-family: Verdana;"><span style="font-weight: bold;">Important Note:</span>&nbsp; this release V3.0.0 is not backward compatible with V2.2.0<br>
</span></li>
</ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 210px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V2.2.0 / 09-February-2015 <o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>









            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>





            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>




            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>

            <span style="font-size: 10pt; font-family: Verdana;"></span>
            


            <span style="font-size: 10pt; font-family: Verdana;"></span><ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">Magnetometer </span><span style="font-size: 10pt; font-family: Verdana;">driver function prototypes added (magneto.h file)</span></li><li><span style="font-size: 10pt; font-family: Verdana;">Update "idd.h" file to provide DeInit() and WakeUp() services in IDD current measurement driver</span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 210px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V2.1.0 / 06-February-2015 <o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>









            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>





            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>




            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>

            <span style="font-size: 10pt; font-family: Verdana;"></span>
            


            <span style="font-size: 10pt; font-family: Verdana;"></span><ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">IDD current measurement </span><span style="font-size: 10pt; font-family: Verdana;">driver function prototypes added (idd.h file)</span></li><li><span style="font-size: 10pt; font-family: Verdana;">io.h: add new typedef enum IO_PinState with IO_PIN_RESET and IO_PIN_SET values<br></span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 210px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V2.0.0 / 15-December-2014 <o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>









            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>





            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>




            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>

            <span style="font-size: 10pt; font-family: Verdana;"></span>
            


            <span style="font-size: 10pt; font-family: Verdana;"></span><ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;">Update "io.h" file to support MFX (Multi Function eXpander) device available on some STM32 boards</span></li><ul><li><span style="font-size: 10pt; font-family: Verdana;">add new entries for IO_ModeTypedef enumeration structure</span><span style="font-size: 10pt; font-family: Verdana;"></span></li><li><span style="font-size: 10pt; font-family: Verdana;">update the IO_DrvTypeDef structure</span></li><ul><li><span style="font-size: 10pt; font-family: Verdana;">Update all return values and function parameters to uint32_t</span></li><li><span style="font-size: 10pt; font-family: Verdana;">Add a return value for Config field</span></li></ul></ul><li style="font-family: Verdana;"><small><span style="font-weight: bold;">Important Note</span>: &nbsp;this version V2.0.0 is not backward compatible with V1.2.1</small></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 180px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.2.1 / 02-December-2014 <o:p></o:p></span></h3>
            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>










            


            
            


            
            






            
            
            






            
            
            





            
            
            




            
            
            

            
            


            
            <ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;">gyro.h: change &#8220;__GIRO_H&#8221; by &#8220;__GYRO_H&#8221; to fix compilation issue under Mac OS</span><span style="font-size: 10pt; font-family: Verdana;"></span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 180px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.2.0 / 18-June-2014 <o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>









            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>





            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>




            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>

            <span style="font-size: 10pt; font-family: Verdana;"></span>
            


            <span style="font-size: 10pt; font-family: Verdana;"></span><ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">EPD (</span><span style="font-size: 10pt; font-family: Verdana;">E Paper Display)&nbsp; driver function prototype added (epd.h file)<br>
                </span></li></ul><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 180px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.1.0 / 21-March-2014 <o:p></o:p></span></h3><p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>









            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>





            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>




            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>

            <span style="font-size: 10pt; font-family: Verdana;"></span>
            


            <span style="font-size: 10pt; font-family: Verdana;"></span><ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;">Temperature Sensor driver function prototype added</span></li></ul><span style="font-size: 10pt; font-family: Verdana;"></span><h3 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial; margin-right: 500pt; width: 180px;"><span style="font-size: 10pt; font-family: Arial; color: white;">V1.0.0 / 18-February-2014 <o:p></o:p></span></h3>











            
            
            
            
            
            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt;"><b style=""><u><span style="font-size: 10pt; font-family: Verdana; color: black;">Main
Changes<o:p></o:p></span></u></b></p>









            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span>


            
            <span style="font-size: 10pt; font-family: Verdana;"></span><span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>






            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>





            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>




            
            
            <span style="font-size: 10pt; font-family: Verdana;"></span>

            <span style="font-size: 10pt; font-family: Verdana;"></span>
            


            <span style="font-size: 10pt; font-family: Verdana;"></span><ul style="list-style-type: square;"><li><span style="font-size: 10pt; font-family: Verdana;">First official release with &nbsp;</span><span style="font-size: 10pt; font-family: Verdana;">Accelerometer, </span><span style="font-size: 10pt; font-family: Verdana;">Audio, Camera, Gyroscope, IO, LCD and Touch Screen drivers function prototypes </span><span style="font-size: 10pt; font-family: Verdana;"></span></li></ul><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic; font-weight: bold;"></span></span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic; font-weight: bold;"></span></span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic; font-weight: bold;"></span></span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic; font-weight: bold;"></span></span><span style="font-size: 10pt; font-family: Verdana;"><span style="font-style: italic; font-weight: bold;"></span></span><span style="font-size: 10pt; font-family: Verdana;"></span><h2 style="background: rgb(51, 102, 255) none repeat scroll 0% 50%; -moz-background-clip: initial; -moz-background-origin: initial; -moz-background-inline-policy: initial;"><a name="License"></a><span style="font-size: 12pt; color: white;">License<o:p></o:p></span><br></h2>
            <div style="text-align: justify;"><font size="-1"><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">
Redistribution and use in source and binary forms, with or without
modification, are permitted provided that the following conditions are
met:</span><br>
            </font>
            <ol><li><font size="-1"><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.</span><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;"></span></font></li><li><font size="-1"><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Redistributions
in binary form must reproduce the above copyright notice, this list of
conditions and the following disclaimer in </span><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">the documentation and/or other materials provided with the distribution.</span><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;"></span></font></li><li><font size="-1"><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">Neither the name of STMicroelectronics nor the names of its contributors may be used to endorse or promote products derived </span><br>
                </font>
              </li></ol>
            <font size="-1"><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; from this software without specific prior written permission.</span><br>
            <span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;"></span><br>
            <span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED</span><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;"> WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A </span><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY </span><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, </span><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER</span><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;"> CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR </span><span style="font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;;">OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.</span></font>
            
            </div>
<p class="MsoNormal"><span style="font-size: 10pt; font-family: &quot;Verdana&quot;,&quot;sans-serif&quot;; color: black;"><font size="-1"></font><o:p></o:p></span></p>
<b><span style="font-size: 10pt; font-family: Verdana; color: black;"></span></b>
            
            <div class="MsoNormal" style="text-align: center;" align="center"><span style="color: black;">
            <hr align="center" size="2" width="100%"></span></div>
            <p class="MsoNormal" style="margin: 4.5pt 0cm 4.5pt 18pt; text-align: center;" align="center"><span style="font-size: 10pt; font-family: Verdana; color: black;">For
complete documentation on </span><span style="font-size: 10pt; font-family: Verdana;">STM32<span style="color: black;">&nbsp;Microcontrollers
visit </span><u><span style="color: blue;"><a href="http://www.st.com/internet/mcu/class/1734.jsp" target="_blank">www.st.com/STM32</a></span></u></span><span style="font-size: 10pt; font-family: Verdana;"><a target="_blank" href="http://www.st.com/internet/mcu/family/141.jsp"><u><span style="color: blue;"></span></u></a></span><span style="font-size: 10pt; font-family: Verdana;"><u><span style="color: blue;"></span></u></span><span style="color: black;"><o:p></o:p></span></p>
            </td>
          </tr>
        </tbody>
      </table>
      <p class="MsoNormal"><span style="font-size: 10pt;"><o:p></o:p></span></p>
      </td>
    </tr>
  </tbody>
</table>
</div>
<p class="MsoNormal"><o:p>&nbsp;</o:p></p>
</div>

</body></html>