<?xml version="1.0" encoding="iso-8859-1"?>

<project>
  <fileVersion>2</fileVersion>
  <configuration>
    <name>STM32F429ZI_Nucleo</name>
    <toolchain>
      <name>ARM</name>
    </toolchain>
    <debug>1</debug>
    <settings>
      <name>General</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <version>24</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>ExePath</name>
          <state>STM32F429ZI_Nucleo/Exe</state>
        </option>
        <option>
          <name>ObjPath</name>
          <state>STM32F429ZI_Nucleo/Obj</state>
        </option>
        <option>
          <name>ListPath</name>
          <state>STM32F429ZI_Nucleo/List</state>
        </option>
        <option>
          <name>GEndianMode</name>
          <state>0</state>
        </option>
        <option>
          <name>Input variant</name>
          <version>3</version>
          <state>6</state>
        </option>
        <option>
          <name>Input description</name>
          <state>No specifier n, no float nor long long, no scan set, no assignment suppressing.</state>
        </option>
        <option>
          <name>Output variant</name>
          <version>2</version>
          <state>5</state>
        </option>
        <option>
          <name>Output description</name>
          <state>No specifier a, A, no specifier n, no float nor long long.</state>
        </option>
        <option>
          <name>GOutputBinary</name>
          <state>0</state>
        </option>
        <option>
          <name>OGCoreOrChip</name>
          <state>1</state>
        </option>
        <option>
          <name>GRuntimeLibSelect</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>GRuntimeLibSelectSlave</name>
          <version>0</version>
          <state>2</state>
        </option>
        <option>
          <name>RTDescription</name>
          <state>Use the full configuration of the C/C++ runtime library. Full locale interface, C locale, file descriptor support, multibytes in printf and scanf, and hex floats in strtod.</state>
        </option>
        <option>
          <name>OGProductVersion</name>
          <state>4.41A</state>
        </option>
        <option>
          <name>OGLastSavedByProductVersion</name>
          <state>7.80.4.12487</state>
        </option>
        <option>
          <name>GeneralEnableMisra</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraVerbose</name>
          <state>0</state>
        </option>
        <option>
          <name>OGChipSelectEditMenu</name>
          <state>STM32F429ZI	ST STM32F429ZI</state>
        </option>
        <option>
          <name>GenLowLevelInterface</name>
          <state>1</state>
        </option>
        <option>
          <name>GEndianModeBE</name>
          <state>1</state>
        </option>
        <option>
          <name>OGBufferedTerminalOutput</name>
          <state>0</state>
        </option>
        <option>
          <name>GenStdoutInterface</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>GeneralMisraVer</name>
          <state>0</state>
        </option>
        <option>
          <name>GeneralMisraRules04</name>
          <version>0</version>
          <state>011111111111111110111111111111011111111111111011110100111111111111111111111111111111111111111111101111111111111011111111111111111111111111111</state>
        </option>
        <option>
          <name>RTConfigPath2</name>
          <state>$TOOLKIT_DIR$/INC/c/DLib_Config_Full.h</state>
        </option>
        <option>
          <name>GBECoreSlave</name>
          <version>24</version>
          <state>39</state>
        </option>
        <option>
          <name>OGUseCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>OGUseCmsisDspLib</name>
          <state>0</state>
        </option>
        <option>
          <name>GRuntimeLibThreads</name>
          <state>0</state>
        </option>
        <option>
          <name>CoreVariant</name>
          <version>24</version>
          <state>39</state>
        </option>
        <option>
          <name>GFPUDeviceSlave</name>
          <state>STM32F429ZI	ST STM32F429ZI</state>
        </option>
        <option>
          <name>FPU2</name>
          <version>0</version>
          <state>4</state>
        </option>
        <option>
          <name>NrRegs</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>NEON</name>
          <state>0</state>
        </option>
        <option>
          <name>GFPUCoreSlave2</name>
          <version>24</version>
          <state>39</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>ICCARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>31</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>CCOptimizationNoSizeConstraints</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDefines</name>
          <state>STM32F429xx</state>
          <state>USE_HAL_DRIVER</state>
          <state>USE_STM32F4XX_NUCLEO_144</state>
        </option>
        <option>
          <name>CCPreprocFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocComments</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPreprocLine</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMnemonics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListCMessages</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssFile</name>
          <state>0</state>
        </option>
        <option>
          <name>CCListAssSource</name>
          <state>0</state>
        </option>
        <option>
          <name>CCEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagSuppress</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagRemark</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagWarning</name>
          <state></state>
        </option>
        <option>
          <name>CCDiagError</name>
          <state></state>
        </option>
        <option>
          <name>CCObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>CCAllowList</name>
          <version>1</version>
          <state>11111110</state>
        </option>
        <option>
          <name>CCDebugInfo</name>
          <state>1</state>
        </option>
        <option>
          <name>IEndianMode</name>
          <state>1</state>
        </option>
        <option>
          <name>IProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>IExtraOptionsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>IExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>CCLangConformance</name>
          <state>0</state>
        </option>
        <option>
          <name>CCSignedPlainChar</name>
          <state>1</state>
        </option>
        <option>
          <name>CCRequirePrototypes</name>
          <state>1</state>
        </option>
        <option>
          <name>CCMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>CCDiagWarnAreErr</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCompilerRuntimeInfo</name>
          <state>0</state>
        </option>
        <option>
          <name>IFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>OutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>CCLibConfigHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>PreInclude</name>
          <state></state>
        </option>
        <option>
          <name>CompilerMisraOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>CCIncludePath2</name>
          <state>$PROJ_DIR$/../Inc</state>
          <state>$PROJ_DIR$/../Drivers/CMSIS/Device/ST/STM32F4xx/Include</state>
          <state>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Inc</state>
          <state>$PROJ_DIR$/../Drivers/BSP/STM32F4xx_Nucleo_144</state>
          <state>$PROJ_DIR$/../Drivers/BSP/Components/Common</state>
          <state>$PROJ_DIR$/../Middlewares/ST/STM32_USB_Host_Library/Core/Inc</state>
          <state>$PROJ_DIR$/../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc</state>
          <state>$PROJ_DIR$/../Middlewares/Third_Party/FatFs/src</state>
        </option>
        <option>
          <name>CCStdIncCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>CCCodeSection</name>
          <state>.text</state>
        </option>
        <option>
          <name>IInterwork2</name>
          <state>0</state>
        </option>
        <option>
          <name>IProcessorMode2</name>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevel</name>
          <state>3</state>
        </option>
        <option>
          <name>CCOptStrategy</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CCOptLevelSlave</name>
          <state>3</state>
        </option>
        <option>
          <name>CompilerMisraRules98</name>
          <version>0</version>
          <state>1000111110110101101110011100111111101110011011000101110111101101100111111111111100110011111001110111001111111111111111111111111</state>
        </option>
        <option>
          <name>CompilerMisraRules04</name>
          <version>0</version>
          <state>111101110010111111111000110111111111111111111111111110010111101111010101111111111111111111111111101111111011111001111011111011111111111111111</state>
        </option>
        <option>
          <name>CCPosIndRopi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndRwpi</name>
          <state>0</state>
        </option>
        <option>
          <name>CCPosIndNoDynInit</name>
          <state>0</state>
        </option>
        <option>
          <name>IccLang</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccAllowVLA</name>
          <state>0</state>
        </option>
        <option>
          <name>IccCppDialect</name>
          <state>1</state>
        </option>
        <option>
          <name>IccExceptions</name>
          <state>1</state>
        </option>
        <option>
          <name>IccRTTI</name>
          <state>1</state>
        </option>
        <option>
          <name>IccStaticDestr</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCppInlineSemantics</name>
          <state>1</state>
        </option>
        <option>
          <name>IccCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IccFloatSemantics</name>
          <state>0</state>
        </option>
        <option>
          <name>CCNoLiteralPool</name>
          <state>0</state>
        </option>
        <option>
          <name>CCOptStrategySlave</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CCGuardCalls</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>AARM</name>
      <archiveVersion>2</archiveVersion>
      <data>
        <version>9</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>AObjPrefix</name>
          <state>1</state>
        </option>
        <option>
          <name>AEndian</name>
          <state>1</state>
        </option>
        <option>
          <name>ACaseSensitivity</name>
          <state>1</state>
        </option>
        <option>
          <name>MacroChars</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>AWarnEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnWhat</name>
          <state>0</state>
        </option>
        <option>
          <name>AWarnOne</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange1</name>
          <state></state>
        </option>
        <option>
          <name>AWarnRange2</name>
          <state></state>
        </option>
        <option>
          <name>ADebug</name>
          <state>1</state>
        </option>
        <option>
          <name>AltRegisterNames</name>
          <state>0</state>
        </option>
        <option>
          <name>ADefines</name>
          <state></state>
        </option>
        <option>
          <name>AList</name>
          <state>0</state>
        </option>
        <option>
          <name>AListHeader</name>
          <state>1</state>
        </option>
        <option>
          <name>AListing</name>
          <state>1</state>
        </option>
        <option>
          <name>Includes</name>
          <state>0</state>
        </option>
        <option>
          <name>MacDefs</name>
          <state>0</state>
        </option>
        <option>
          <name>MacExps</name>
          <state>1</state>
        </option>
        <option>
          <name>MacExec</name>
          <state>0</state>
        </option>
        <option>
          <name>OnlyAssed</name>
          <state>0</state>
        </option>
        <option>
          <name>MultiLine</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLengthCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>PageLength</name>
          <state>80</state>
        </option>
        <option>
          <name>TabSpacing</name>
          <state>8</state>
        </option>
        <option>
          <name>AXRef</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDefines</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefInternal</name>
          <state>0</state>
        </option>
        <option>
          <name>AXRefDual</name>
          <state>0</state>
        </option>
        <option>
          <name>AProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AFpuProcessor</name>
          <state>1</state>
        </option>
        <option>
          <name>AOutputFile</name>
          <state>$FILE_BNAME$.o</state>
        </option>
        <option>
          <name>AMultibyteSupport</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsCheck</name>
          <state>0</state>
        </option>
        <option>
          <name>ALimitErrorsEdit</name>
          <state>100</state>
        </option>
        <option>
          <name>AIgnoreStdInclude</name>
          <state>0</state>
        </option>
        <option>
          <name>AUserIncludes</name>
          <state></state>
        </option>
        <option>
          <name>AExtraOptionsCheckV2</name>
          <state>0</state>
        </option>
        <option>
          <name>AExtraOptionsV2</name>
          <state></state>
        </option>
        <option>
          <name>AsmNoLiteralPool</name>
          <state>0</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>OBJCOPY</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>1</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>OOCOutputFormat</name>
          <version>3</version>
          <state>1</state>
        </option>
        <option>
          <name>OCOutputOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCOutputFile</name>
          <state>STM32F429ZI_Nucleo_144.hex</state>
        </option>
        <option>
          <name>OOCCommandLineProducer</name>
          <state>1</state>
        </option>
        <option>
          <name>OOCObjCopyEnable</name>
          <state>1</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>CUSTOM</name>
      <archiveVersion>3</archiveVersion>
      <data>
        <extensions></extensions>
        <cmdline></cmdline>
        <hasPrio>0</hasPrio>
      </data>
    </settings>
    <settings>
      <name>BICOMP</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
    <settings>
      <name>BUILDACTION</name>
      <archiveVersion>1</archiveVersion>
      <data>
        <prebuild></prebuild>
        <postbuild></postbuild>
      </data>
    </settings>
    <settings>
      <name>ILINK</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>18</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>IlinkLibIOConfig</name>
          <state>1</state>
        </option>
        <option>
          <name>XLinkMisraHandler</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkInputFileSlave</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOutputFile</name>
          <state>STM32F429ZI_Nucleo_144.out</state>
        </option>
        <option>
          <name>IlinkDebugInfoEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkKeepSymbols</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinaryFile</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinarySymbol</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinarySegment</name>
          <state></state>
        </option>
        <option>
          <name>IlinkRawBinaryAlign</name>
          <state></state>
        </option>
        <option>
          <name>IlinkDefines</name>
          <state></state>
        </option>
        <option>
          <name>IlinkConfigDefines</name>
          <state></state>
        </option>
        <option>
          <name>IlinkMapFile</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogFile</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogInitialization</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogModule</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogSection</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogVeneer</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfOverride</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkIcfFile</name>
          <state>$PROJ_DIR$/stm32f429xx_flash.icf</state>
        </option>
        <option>
          <name>IlinkIcfFileSlave</name>
          <state></state>
        </option>
        <option>
          <name>IlinkEnableRemarks</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkSuppressDiags</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsRem</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsWarn</name>
          <state></state>
        </option>
        <option>
          <name>IlinkTreatAsErr</name>
          <state></state>
        </option>
        <option>
          <name>IlinkWarningsAreErrors</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkUseExtraOptions</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkExtraOptions</name>
          <state></state>
        </option>
        <option>
          <name>IlinkLowLevelInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAutoLibEnable</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkAdditionalLibs</name>
          <state></state>
        </option>
        <option>
          <name>IlinkOverrideProgramEntryLabel</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabelSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkProgramEntryLabel</name>
          <state>__iar_program_start</state>
        </option>
        <option>
          <name>DoFill</name>
          <state>0</state>
        </option>
        <option>
          <name>FillerByte</name>
          <state>0xFF</state>
        </option>
        <option>
          <name>FillerStart</name>
          <state>0x0</state>
        </option>
        <option>
          <name>FillerEnd</name>
          <state>0x0</state>
        </option>
        <option>
          <name>CrcSize</name>
          <version>0</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcAlign</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcPoly</name>
          <state>0x11021</state>
        </option>
        <option>
          <name>CrcCompl</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcBitOrder</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>CrcInitialValue</name>
          <state>0x0</state>
        </option>
        <option>
          <name>DoCrc</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkBE8Slave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkBufferedTerminalOutput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkStdoutInterfaceSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>CrcFullSize</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIElfToolPostProcess</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogAutoLibSelect</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogRedirSymbols</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkLogUnusedFragments</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcReverseByteOrder</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCrcUseAsInput</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptInline</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptExceptionsAllow</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptExceptionsForce</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkCmsis</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptMergeDuplSections</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkOptUseVfe</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkOptForceVfe</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackAnalysisEnable</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkStackControlFile</name>
          <state></state>
        </option>
        <option>
          <name>IlinkStackCallGraphFile</name>
          <state></state>
        </option>
        <option>
          <name>CrcAlgorithm</name>
          <version>1</version>
          <state>1</state>
        </option>
        <option>
          <name>CrcUnitSize</name>
          <version>0</version>
          <state>0</state>
        </option>
        <option>
          <name>IlinkThreadsSlave</name>
          <state>1</state>
        </option>
        <option>
          <name>IlinkLogCallGraph</name>
          <state>0</state>
        </option>
        <option>
          <name>IlinkIcfFile_AltDefault</name>
          <state></state>
        </option>
      </data>
    </settings>
    <settings>
      <name>IARCHIVE</name>
      <archiveVersion>0</archiveVersion>
      <data>
        <version>0</version>
        <wantNonLocal>1</wantNonLocal>
        <debug>1</debug>
        <option>
          <name>IarchiveInputs</name>
          <state></state>
        </option>
        <option>
          <name>IarchiveOverride</name>
          <state>0</state>
        </option>
        <option>
          <name>IarchiveOutput</name>
          <state>###Unitialized###</state>
        </option>
      </data>
    </settings>
    <settings>
      <name>BILINK</name>
      <archiveVersion>0</archiveVersion>
      <data/>
    </settings>
  </configuration>
  <group>
    <name>Application</name>
    <group>
      <name>EWARM</name>
      <file>
        <name>$PROJ_DIR$/startup_stm32f429xx.s</name>
      </file>
    </group>
    <group>
      <name>User</name>
      <file>
        <name>$PROJ_DIR$/../Src/main.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Src/stm32f4xx_it.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Src/usbh_conf.c</name>
      </file>
    </group>
  </group>
  <group>
    <name>Doc</name>
    <file>
      <name>$PROJ_DIR$/../readme.txt</name>
    </file>
  </group>
  <group>
    <name>Drivers</name>
    <group>
      <name>BSP</name>
      <group>
        <name>STM32F4xx_Nucleo</name>
        <file>
          <name>$PROJ_DIR$/../Drivers/BSP/STM32F4xx_Nucleo_144/stm32f4xx_nucleo_144.c</name>
        </file>
      </group>
    </group>
    <group>
      <name>CMSIS</name>
      <file>
        <name>$PROJ_DIR$/../Src/system_stm32f4xx.c</name>
      </file>
    </group>
    <group>
      <name>STM32F4xx_HAL_Driver</name>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_hcd.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sai.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sai_ex.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sdram.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_fmc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_sdmmc.c</name>
      </file>
      <file>
        <name>$PROJ_DIR$/../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_usb.c</name>
      </file>
    </group>
  </group>
  <group>
    <name>Middlewares</name>
    <group>
      <name>FatFs</name>
      <group>
        <name>Core</name>
        <file>
          <name>$PROJ_DIR$/../Middlewares/Third_Party/FatFs/src/diskio.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$/../Middlewares/Third_Party/FatFs/src/ff.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$/../Middlewares/Third_Party/FatFs/src/ff_gen_drv.c</name>
        </file>
      </group>
      <group>
        <name>Drivers</name>
        <file>
          <name>$PROJ_DIR$/../Src/usbh_diskio_dma.c</name>
        </file>
      </group>
    </group>
    <group>
      <name>STM32_USBH_Library</name>
      <group>
        <name>Class</name>
        <group>
          <name>MSC</name>
          <file>
            <name>$PROJ_DIR$/../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc.c</name>
          </file>
          <file>
            <name>$PROJ_DIR$/../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_bot.c</name>
          </file>
          <file>
            <name>$PROJ_DIR$/../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_scsi.c</name>
          </file>
        </group>
      </group>
      <group>
        <name>Core</name>
        <file>
          <name>$PROJ_DIR$/../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_core.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$/../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ctlreq.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$/../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ioreq.c</name>
        </file>
        <file>
          <name>$PROJ_DIR$/../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_pipes.c</name>
        </file>
      </group>
    </group>
  </group>
</project>


