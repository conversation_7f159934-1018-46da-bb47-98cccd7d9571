/**
  ******************************************************************************
  * @file    FatFs/FatFs_USBDisk/Inc/main.h 
  * <AUTHOR> Application Team
  * @brief   Header for main.c module
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __MAIN_H
#define __MAIN_H

/* Includes ------------------------------------------------------------------*/
#include "stm32f4xx_hal.h"

/* FatFs includes component */
#include "ff_gen_drv.h"
#include "usbh_diskio_dma.h"

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/

/* LED definitions for STM32F429IGT6 custom board */
#define LED_RED_PIN                    GPIO_PIN_10
#define LED_RED_GPIO_PORT              GPIOH
#define LED_RED_GPIO_CLK_ENABLE()      __HAL_RCC_GPIOH_CLK_ENABLE()

#define LED_GREEN_PIN                  GPIO_PIN_11
#define LED_GREEN_GPIO_PORT            GPIOH
#define LED_GREEN_GPIO_CLK_ENABLE()    __HAL_RCC_GPIOH_CLK_ENABLE()

#define LED_BLUE_PIN                   GPIO_PIN_12
#define LED_BLUE_GPIO_PORT             GPIOH
#define LED_BLUE_GPIO_CLK_ENABLE()     __HAL_RCC_GPIOH_CLK_ENABLE()

/* LED control macros */
#define LED_RED_ON()                   HAL_GPIO_WritePin(LED_RED_GPIO_PORT, LED_RED_PIN, GPIO_PIN_SET)
#define LED_RED_OFF()                  HAL_GPIO_WritePin(LED_RED_GPIO_PORT, LED_RED_PIN, GPIO_PIN_RESET)
#define LED_GREEN_ON()                 HAL_GPIO_WritePin(LED_GREEN_GPIO_PORT, LED_GREEN_PIN, GPIO_PIN_SET)
#define LED_GREEN_OFF()                HAL_GPIO_WritePin(LED_GREEN_GPIO_PORT, LED_GREEN_PIN, GPIO_PIN_RESET)
#define LED_BLUE_ON()                  HAL_GPIO_WritePin(LED_BLUE_GPIO_PORT, LED_BLUE_PIN, GPIO_PIN_SET)
#define LED_BLUE_OFF()                 HAL_GPIO_WritePin(LED_BLUE_GPIO_PORT, LED_BLUE_PIN, GPIO_PIN_RESET)

/* Compatibility macros for original code */
#define BSP_LED_Init(Led)              Custom_LED_Init()
#define BSP_LED_On(Led)                ((Led == LED1) ? LED_GREEN_ON() : LED_RED_ON())
#define BSP_LED_Off(Led)               ((Led == LED1) ? LED_GREEN_OFF() : LED_RED_OFF())

/* LED definitions for compatibility */
typedef enum
{
  LED1 = 0,
  LED3 = 1
} Led_TypeDef;

/* UART Debug definitions for STM32F429IGT6 */
#define DEBUG_UART                     USART1
#define DEBUG_UART_CLK_ENABLE()        __HAL_RCC_USART1_CLK_ENABLE()
#define DEBUG_UART_RX_GPIO_CLK_ENABLE() __HAL_RCC_GPIOA_CLK_ENABLE()
#define DEBUG_UART_TX_GPIO_CLK_ENABLE() __HAL_RCC_GPIOA_CLK_ENABLE()

#define DEBUG_UART_FORCE_RESET()       __HAL_RCC_USART1_FORCE_RESET()
#define DEBUG_UART_RELEASE_RESET()     __HAL_RCC_USART1_RELEASE_RESET()

/* Definition for USARTx Pins */
#define DEBUG_UART_TX_PIN              GPIO_PIN_9
#define DEBUG_UART_TX_GPIO_PORT        GPIOA
#define DEBUG_UART_TX_AF               GPIO_AF7_USART1
#define DEBUG_UART_RX_PIN              GPIO_PIN_10
#define DEBUG_UART_RX_GPIO_PORT        GPIOA
#define DEBUG_UART_RX_AF               GPIO_AF7_USART1

/* Function prototypes */
void Custom_LED_Init(void);
void Debug_UART_Init(void);
void Debug_Printf(const char* format, ...);

#endif /* __MAIN_H */
