--cpu=Cortex-M4.fp.sp
"stm32f429igt6_custom\stm32f4xx_it.o"
"stm32f429igt6_custom\usbh_conf.o"
"stm32f429igt6_custom\main.o"
"stm32f429igt6_custom\startup_stm32f429xx.o"
"stm32f429igt6_custom\usbh_pipes.o"
"stm32f429igt6_custom\usbh_ioreq.o"
"stm32f429igt6_custom\usbh_core.o"
"stm32f429igt6_custom\usbh_ctlreq.o"
"stm32f429igt6_custom\ff_gen_drv.o"
"stm32f429igt6_custom\diskio.o"
"stm32f429igt6_custom\ff.o"
"stm32f429igt6_custom\usbh_diskio_dma.o"
"stm32f429igt6_custom\stm32f4xx_ll_usb.o"
"stm32f429igt6_custom\stm32f4xx_hal_i2c_ex.o"
"stm32f429igt6_custom\stm32f4xx_hal_dma_ex.o"
"stm32f429igt6_custom\stm32f4xx_hal_rcc_ex.o"
"stm32f429igt6_custom\stm32f4xx_ll_sdmmc.o"
"stm32f429igt6_custom\stm32f4xx_hal.o"
"stm32f429igt6_custom\stm32f4xx_hal_rcc.o"
"stm32f429igt6_custom\stm32f4xx_hal_pwr_ex.o"
"stm32f429igt6_custom\stm32f4xx_hal_uart.o"
"stm32f429igt6_custom\stm32f4xx_hal_sai_ex.o"
"stm32f429igt6_custom\stm32f4xx_hal_gpio.o"
"stm32f429igt6_custom\stm32f4xx_hal_spi.o"
"stm32f429igt6_custom\stm32f4xx_hal_dma.o"
"stm32f429igt6_custom\stm32f4xx_hal_i2c.o"
"stm32f429igt6_custom\stm32f4xx_hal_sdram.o"
"stm32f429igt6_custom\stm32f4xx_hal_sai.o"
"stm32f429igt6_custom\stm32f4xx_ll_fmc.o"
"stm32f429igt6_custom\stm32f4xx_hal_hcd.o"
"stm32f429igt6_custom\stm32f4xx_hal_cortex.o"
"stm32f429igt6_custom\stm32f4xx_hal_pwr.o"
"stm32f429igt6_custom\usbh_msc_bot.o"
"stm32f429igt6_custom\usbh_msc.o"
"stm32f429igt6_custom\usbh_msc_scsi.o"
"stm32f429igt6_custom\system_stm32f4xx.o"
--library_type=microlib --strict --scatter "STM32F429IGT6_Custom\STM32F429IGT6_Custom.sct"
--summary_stderr --info summarysizes --map --load_addr_map_info --xref --callgraph --symbols
--info sizes --info totals --info unused --info veneers
--list "STM32F429IGT6_Custom.map" -o STM32F429IGT6_Custom\STM32F429IGT6_Custom.axf