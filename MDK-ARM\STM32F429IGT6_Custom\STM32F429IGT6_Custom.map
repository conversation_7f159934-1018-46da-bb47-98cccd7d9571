Component: Arm Compiler for Embedded 6.23 Tool: armlink [5f102400]

==============================================================================

Section Cross References

    stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler) refers to stm32f4xx_it.o(.text.NMI_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler) refers to stm32f4xx_it.o(.text.HardFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler) refers to stm32f4xx_it.o(.text.MemManage_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler) refers to stm32f4xx_it.o(.text.BusFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler) refers to stm32f4xx_it.o(.text.SVC_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler) refers to stm32f4xx_it.o(.text.PendSV_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.SysTick_Handler) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler) refers to stm32f4xx_it.o(.text.SysTick_Handler) for [Anonymous Symbol]
    stm32f4xx_it.o(.text.OTG_HS_IRQHandler) refers to usbh_conf.o(.bss.hhcd) for hhcd
    stm32f4xx_it.o(.text.OTG_HS_IRQHandler) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) for HAL_HCD_IRQHandler
    stm32f4xx_it.o(.ARM.exidx.text.OTG_HS_IRQHandler) refers to stm32f4xx_it.o(.text.OTG_HS_IRQHandler) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usbh_conf.o(.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspInit) refers to usbh_conf.o(.text.HAL_HCD_MspInit) for [Anonymous Symbol]
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspDeInit) refers to usbh_conf.o(.text.HAL_HCD_MspDeInit) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_SOF_Callback) refers to usbh_core.o(.text.USBH_LL_IncTimer) for USBH_LL_IncTimer
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_SOF_Callback) refers to usbh_conf.o(.text.HAL_HCD_SOF_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_Connect_Callback) refers to usbh_core.o(.text.USBH_LL_Connect) for USBH_LL_Connect
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_Connect_Callback) refers to usbh_conf.o(.text.HAL_HCD_Connect_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) refers to usbh_core.o(.text.USBH_LL_Disconnect) for USBH_LL_Disconnect
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback) refers to usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) refers to usbh_core.o(.text.USBH_LL_PortEnabled) for USBH_LL_PortEnabled
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback) refers to usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) refers to usbh_core.o(.text.USBH_LL_PortDisabled) for USBH_LL_PortDisabled
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback) refers to usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) for [Anonymous Symbol]
    usbh_conf.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback) refers to usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_Init) refers to usbh_conf.o(.bss.hhcd) for hhcd
    usbh_conf.o(.text.USBH_LL_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) for HAL_HCD_Init
    usbh_conf.o(.text.USBH_LL_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame) for HAL_HCD_GetCurrentFrame
    usbh_conf.o(.text.USBH_LL_Init) refers to usbh_core.o(.text.USBH_LL_SetTimer) for USBH_LL_SetTimer
    usbh_conf.o(.ARM.exidx.text.USBH_LL_Init) refers to usbh_conf.o(.text.USBH_LL_Init) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_DeInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) for HAL_HCD_DeInit
    usbh_conf.o(.ARM.exidx.text.USBH_LL_DeInit) refers to usbh_conf.o(.text.USBH_LL_DeInit) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_Start) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) for HAL_HCD_Start
    usbh_conf.o(.ARM.exidx.text.USBH_LL_Start) refers to usbh_conf.o(.text.USBH_LL_Start) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_Stop) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop) for HAL_HCD_Stop
    usbh_conf.o(.ARM.exidx.text.USBH_LL_Stop) refers to usbh_conf.o(.text.USBH_LL_Stop) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetSpeed) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed) for HAL_HCD_GetCurrentSpeed
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetSpeed) refers to usbh_conf.o(.text.USBH_LL_GetSpeed) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_ResetPort) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort) for HAL_HCD_ResetPort
    usbh_conf.o(.ARM.exidx.text.USBH_LL_ResetPort) refers to usbh_conf.o(.text.USBH_LL_ResetPort) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetLastXferSize) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount) for HAL_HCD_HC_GetXferCount
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetLastXferSize) refers to usbh_conf.o(.text.USBH_LL_GetLastXferSize) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_OpenPipe) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) for HAL_HCD_HC_Init
    usbh_conf.o(.ARM.exidx.text.USBH_LL_OpenPipe) refers to usbh_conf.o(.text.USBH_LL_OpenPipe) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_ClosePipe) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt) for HAL_HCD_HC_Halt
    usbh_conf.o(.ARM.exidx.text.USBH_LL_ClosePipe) refers to usbh_conf.o(.text.USBH_LL_ClosePipe) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_SubmitURB) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest) for HAL_HCD_HC_SubmitRequest
    usbh_conf.o(.ARM.exidx.text.USBH_LL_SubmitURB) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetURBState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState) for HAL_HCD_HC_GetURBState
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetURBState) refers to usbh_conf.o(.text.USBH_LL_GetURBState) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_DriverVBUS) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    usbh_conf.o(.text.USBH_LL_DriverVBUS) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    usbh_conf.o(.ARM.exidx.text.USBH_LL_DriverVBUS) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_SetToggle) refers to usbh_conf.o(.bss.hhcd) for hhcd
    usbh_conf.o(.ARM.exidx.text.USBH_LL_SetToggle) refers to usbh_conf.o(.text.USBH_LL_SetToggle) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_LL_GetToggle) refers to usbh_conf.o(.bss.hhcd) for hhcd
    usbh_conf.o(.ARM.exidx.text.USBH_LL_GetToggle) refers to usbh_conf.o(.text.USBH_LL_GetToggle) for [Anonymous Symbol]
    usbh_conf.o(.text.USBH_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    usbh_conf.o(.ARM.exidx.text.USBH_Delay) refers to usbh_conf.o(.text.USBH_Delay) for [Anonymous Symbol]
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Init) for HAL_Init
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(.text.main) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(.text.main) refers to main.o(.text.Custom_LED_Init) for Custom_LED_Init
    main.o(.text.main) refers to main.o(.text.Debug_UART_Init) for Debug_UART_Init
    main.o(.text.main) refers to main.o(.text.Debug_Printf) for Debug_Printf
    main.o(.text.main) refers to main.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    main.o(.text.main) refers to usbh_diskio_dma.o(.rodata.USBH_Driver) for USBH_Driver
    main.o(.text.main) refers to ff_gen_drv.o(.text.FATFS_LinkDriver) for FATFS_LinkDriver
    main.o(.text.main) refers to main.o(.bss.hUSBHost) for hUSBHost
    main.o(.text.main) refers to main.o(.text.USBH_UserProcess) for USBH_UserProcess
    main.o(.text.main) refers to usbh_core.o(.text.USBH_Init) for USBH_Init
    main.o(.text.main) refers to usbh_msc.o(.data.USBH_msc) for USBH_msc
    main.o(.text.main) refers to usbh_core.o(.text.USBH_RegisterClass) for USBH_RegisterClass
    main.o(.text.main) refers to usbh_core.o(.text.USBH_Start) for USBH_Start
    main.o(.text.main) refers to main.o(.rodata.str1.4) for .L__const.MSC_Application.wtext
    main.o(.text.main) refers to main.o(.bss.MyFile) for MyFile
    main.o(.text.main) refers to usbh_core.o(.text.USBH_Process) for USBH_Process
    main.o(.text.main) refers to main.o(.rodata.str1.1) for .L.str.4
    main.o(.text.main) refers to main.o(.bss.USBDISKFatFs) for USBDISKFatFs
    main.o(.text.main) refers to ff.o(.text.f_mount) for f_mount
    main.o(.text.main) refers to ff.o(.text.f_open) for f_open
    main.o(.text.main) refers to ff.o(.text.f_write) for f_write
    main.o(.text.main) refers to ff.o(.text.f_close) for f_close
    main.o(.text.main) refers to ff.o(.text.f_read) for f_read
    main.o(.text.main) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(.text.main) refers to ff_gen_drv.o(.text.FATFS_UnLinkDriver) for FATFS_UnLinkDriver
    main.o(.text.main) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.text.main) refers to main.o(.text.Error_Handler) for Error_Handler
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.Custom_LED_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(.text.Custom_LED_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(.ARM.exidx.text.Custom_LED_Init) refers to main.o(.text.Custom_LED_Init) for [Anonymous Symbol]
    main.o(.text.Debug_UART_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    main.o(.text.Debug_UART_Init) refers to main.o(.bss.huart1) for huart1
    main.o(.text.Debug_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for HAL_UART_Init
    main.o(.ARM.exidx.text.Debug_UART_Init) refers to main.o(.text.Debug_UART_Init) for [Anonymous Symbol]
    main.o(.text.Debug_Printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    main.o(.text.Debug_Printf) refers to strlen.o(.text) for strlen
    main.o(.text.Debug_Printf) refers to main.o(.bss.huart1) for huart1
    main.o(.text.Debug_Printf) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for HAL_UART_Transmit
    main.o(.ARM.exidx.text.Debug_Printf) refers to main.o(.text.Debug_Printf) for [Anonymous Symbol]
    main.o(.text.USBH_UserProcess) refers to main.o(.text.Debug_Printf) for Debug_Printf
    main.o(.text.USBH_UserProcess) refers to main.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    main.o(.text.USBH_UserProcess) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(.text.USBH_UserProcess) refers to main.o(.rodata.str1.1) for .L.str.30
    main.o(.text.USBH_UserProcess) refers to ff.o(.text.f_mount) for f_mount
    main.o(.ARM.exidx.text.USBH_UserProcess) refers to main.o(.text.USBH_UserProcess) for [Anonymous Symbol]
    main.o(.text.Error_Handler) refers to main.o(.rodata.str1.1) for .L.str.27
    main.o(.text.Error_Handler) refers to main.o(.text.Debug_Printf) for Debug_Printf
    main.o(.text.Error_Handler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    main.o(.text.Error_Handler) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    main.o(.ARM.exidx.text.Error_Handler) refers to main.o(.text.Error_Handler) for [Anonymous Symbol]
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    startup_stm32f429xx.o(RESET) refers to startup_stm32f429xx.o(.text) for Reset_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.NMI_Handler) for NMI_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.HardFault_Handler) for HardFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.MemManage_Handler) for MemManage_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.BusFault_Handler) for BusFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.SVC_Handler) for SVC_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.PendSV_Handler) for PendSV_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.SysTick_Handler) for SysTick_Handler
    startup_stm32f429xx.o(RESET) refers to stm32f4xx_it.o(.text.OTG_HS_IRQHandler) for OTG_HS_IRQHandler
    startup_stm32f429xx.o(.text) refers to system_stm32f4xx.o(.text.SystemInit) for SystemInit
    startup_stm32f429xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    usbh_pipes.o(.text.USBH_OpenPipe) refers to usbh_conf.o(.text.USBH_LL_OpenPipe) for USBH_LL_OpenPipe
    usbh_pipes.o(.ARM.exidx.text.USBH_OpenPipe) refers to usbh_pipes.o(.text.USBH_OpenPipe) for [Anonymous Symbol]
    usbh_pipes.o(.text.USBH_ClosePipe) refers to usbh_conf.o(.text.USBH_LL_ClosePipe) for USBH_LL_ClosePipe
    usbh_pipes.o(.ARM.exidx.text.USBH_ClosePipe) refers to usbh_pipes.o(.text.USBH_ClosePipe) for [Anonymous Symbol]
    usbh_pipes.o(.ARM.exidx.text.USBH_AllocPipe) refers to usbh_pipes.o(.text.USBH_AllocPipe) for [Anonymous Symbol]
    usbh_pipes.o(.ARM.exidx.text.USBH_FreePipe) refers to usbh_pipes.o(.text.USBH_FreePipe) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_CtlSendSetup) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendSetup) refers to usbh_ioreq.o(.text.USBH_CtlSendSetup) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_CtlSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendData) refers to usbh_ioreq.o(.text.USBH_CtlSendData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_CtlReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_CtlReceiveData) refers to usbh_ioreq.o(.text.USBH_CtlReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_BulkSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_BulkSendData) refers to usbh_ioreq.o(.text.USBH_BulkSendData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_BulkReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_BulkReceiveData) refers to usbh_ioreq.o(.text.USBH_BulkReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_InterruptReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptReceiveData) refers to usbh_ioreq.o(.text.USBH_InterruptReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_InterruptSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptSendData) refers to usbh_ioreq.o(.text.USBH_InterruptSendData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_IsocReceiveData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_IsocReceiveData) refers to usbh_ioreq.o(.text.USBH_IsocReceiveData) for [Anonymous Symbol]
    usbh_ioreq.o(.text.USBH_IsocSendData) refers to usbh_conf.o(.text.USBH_LL_SubmitURB) for USBH_LL_SubmitURB
    usbh_ioreq.o(.ARM.exidx.text.USBH_IsocSendData) refers to usbh_ioreq.o(.text.USBH_IsocSendData) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Init) refers to memseta.o(.text) for __aeabi_memclr4
    usbh_core.o(.text.USBH_Init) refers to usbh_conf.o(.text.USBH_LL_Init) for USBH_LL_Init
    usbh_core.o(.ARM.exidx.text.USBH_Init) refers to usbh_core.o(.text.USBH_Init) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_DeInit) refers to memseta.o(.text) for __aeabi_memclr4
    usbh_core.o(.text.USBH_DeInit) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.ARM.exidx.text.USBH_DeInit) refers to usbh_core.o(.text.USBH_DeInit) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_RegisterClass) refers to usbh_core.o(.text.USBH_RegisterClass) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_SelectInterface) refers to usbh_core.o(.text.USBH_SelectInterface) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_GetActiveClass) refers to usbh_core.o(.text.USBH_GetActiveClass) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_FindInterface) refers to usbh_core.o(.text.USBH_FindInterface) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_FindInterfaceIndex) refers to usbh_core.o(.text.USBH_FindInterfaceIndex) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Start) refers to usbh_conf.o(.text.USBH_LL_Start) for USBH_LL_Start
    usbh_core.o(.text.USBH_Start) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.ARM.exidx.text.USBH_Start) refers to usbh_core.o(.text.USBH_Start) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Stop) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.text.USBH_Stop) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.text.USBH_Stop) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_Stop) refers to usbh_core.o(.text.USBH_Stop) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_ReEnumerate) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.text.USBH_ReEnumerate) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.text.USBH_ReEnumerate) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_ReEnumerate) refers to usbh_core.o(.text.USBH_ReEnumerate) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_IsPortEnabled) refers to usbh_core.o(.text.USBH_IsPortEnabled) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_Delay) for USBH_Delay
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_ResetPort) for USBH_LL_ResetPort
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_Get_DevDesc) for USBH_Get_DevDesc
    usbh_core.o(.text.USBH_Process) refers to usbh_pipes.o(.text.USBH_OpenPipe) for USBH_OpenPipe
    usbh_core.o(.text.USBH_Process) refers to memseta.o(.text) for __aeabi_memclr4
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_Start) for USBH_LL_Start
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_DriverVBUS) for USBH_LL_DriverVBUS
    usbh_core.o(.text.USBH_Process) refers to usbh_conf.o(.text.USBH_LL_GetSpeed) for USBH_LL_GetSpeed
    usbh_core.o(.text.USBH_Process) refers to usbh_pipes.o(.text.USBH_AllocPipe) for USBH_AllocPipe
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_SetCfg) for USBH_SetCfg
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_SetFeature) for USBH_SetFeature
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_Get_CfgDesc) for USBH_Get_CfgDesc
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_SetAddress) for USBH_SetAddress
    usbh_core.o(.text.USBH_Process) refers to usbh_ctlreq.o(.text.USBH_Get_StringDesc) for USBH_Get_StringDesc
    usbh_core.o(.text.USBH_Process) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_Process) refers to usbh_core.o(.text.USBH_Process) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_SetTimer) refers to usbh_core.o(.text.USBH_LL_SetTimer) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_IncTimer) refers to usbh_core.o(.text.USBH_LL_IncTimer) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_PortEnabled) refers to usbh_core.o(.text.USBH_LL_PortEnabled) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_PortDisabled) refers to usbh_core.o(.text.USBH_LL_PortDisabled) for [Anonymous Symbol]
    usbh_core.o(.ARM.exidx.text.USBH_LL_Connect) refers to usbh_core.o(.text.USBH_LL_Connect) for [Anonymous Symbol]
    usbh_core.o(.text.USBH_LL_Disconnect) refers to usbh_conf.o(.text.USBH_LL_Stop) for USBH_LL_Stop
    usbh_core.o(.text.USBH_LL_Disconnect) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_core.o(.ARM.exidx.text.USBH_LL_Disconnect) refers to usbh_core.o(.text.USBH_LL_Disconnect) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_Get_DevDesc) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_DevDesc) refers to usbh_ctlreq.o(.text.USBH_Get_DevDesc) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_GetDescriptor) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_GetDescriptor) refers to usbh_ctlreq.o(.text.USBH_GetDescriptor) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_Get_CfgDesc) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_CfgDesc) refers to usbh_ctlreq.o(.text.USBH_Get_CfgDesc) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_Get_StringDesc) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_StringDesc) refers to usbh_ctlreq.o(.text.USBH_Get_StringDesc) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_ioreq.o(.text.USBH_CtlSendSetup) for USBH_CtlSendSetup
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_ioreq.o(.text.USBH_CtlSendData) for USBH_CtlSendData
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_conf.o(.text.USBH_LL_GetURBState) for USBH_LL_GetURBState
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_ioreq.o(.text.USBH_CtlReceiveData) for USBH_CtlReceiveData
    usbh_ctlreq.o(.text.USBH_CtlReq) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_ctlreq.o(.ARM.exidx.text.USBH_CtlReq) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetAddress) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetAddress) refers to usbh_ctlreq.o(.text.USBH_SetAddress) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetCfg) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetCfg) refers to usbh_ctlreq.o(.text.USBH_SetCfg) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetInterface) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetInterface) refers to usbh_ctlreq.o(.text.USBH_SetInterface) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_SetFeature) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_SetFeature) refers to usbh_ctlreq.o(.text.USBH_SetFeature) for [Anonymous Symbol]
    usbh_ctlreq.o(.text.USBH_ClrFeature) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_ctlreq.o(.ARM.exidx.text.USBH_ClrFeature) refers to usbh_ctlreq.o(.text.USBH_ClrFeature) for [Anonymous Symbol]
    usbh_ctlreq.o(.ARM.exidx.text.USBH_GetNextDesc) refers to usbh_ctlreq.o(.text.USBH_GetNextDesc) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_LinkDriverEx) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriverEx) refers to ff_gen_drv.o(.text.FATFS_LinkDriverEx) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_LinkDriver) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriver) refers to ff_gen_drv.o(.text.FATFS_LinkDriver) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_UnLinkDriverEx) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriverEx) refers to ff_gen_drv.o(.text.FATFS_UnLinkDriverEx) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_UnLinkDriver) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriver) refers to ff_gen_drv.o(.text.FATFS_UnLinkDriver) for [Anonymous Symbol]
    ff_gen_drv.o(.text.FATFS_GetAttachedDriversNbr) refers to ff_gen_drv.o(.bss.disk) for disk
    ff_gen_drv.o(.ARM.exidx.text.FATFS_GetAttachedDriversNbr) refers to ff_gen_drv.o(.text.FATFS_GetAttachedDriversNbr) for [Anonymous Symbol]
    diskio.o(.text.disk_status) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_status) refers to diskio.o(.text.disk_status) for [Anonymous Symbol]
    diskio.o(.text.disk_initialize) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_initialize) refers to diskio.o(.text.disk_initialize) for [Anonymous Symbol]
    diskio.o(.text.disk_read) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_read) refers to diskio.o(.text.disk_read) for [Anonymous Symbol]
    diskio.o(.text.disk_write) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_write) refers to diskio.o(.text.disk_write) for [Anonymous Symbol]
    diskio.o(.text.disk_ioctl) refers to ff_gen_drv.o(.bss.disk) for disk
    diskio.o(.ARM.exidx.text.disk_ioctl) refers to diskio.o(.text.disk_ioctl) for [Anonymous Symbol]
    diskio.o(.ARM.exidx.text.get_fattime) refers to diskio.o(.text.get_fattime) for [Anonymous Symbol]
    ff.o(.text.f_mount) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_mount) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.ARM.exidx.text.f_mount) refers to ff.o(.text.f_mount) for [Anonymous Symbol]
    ff.o(.text.find_volume) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.find_volume) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.find_volume) refers to diskio.o(.text.disk_initialize) for disk_initialize
    ff.o(.text.find_volume) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.find_volume) refers to ff.o(.text.find_volume) for [Anonymous Symbol]
    ff.o(.text.f_open) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_open) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_open) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_open) refers to ff.o(.text.dir_register) for dir_register
    ff.o(.text.f_open) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_open) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_open) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.f_open) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.f_open) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.f_open) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.ARM.exidx.text.f_open) refers to ff.o(.text.f_open) for [Anonymous Symbol]
    ff.o(.text.follow_path) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.follow_path) refers to ff.o(.rodata.ExCvt) for ExCvt
    ff.o(.text.follow_path) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.follow_path) refers to ff.o(.text.follow_path) for [Anonymous Symbol]
    ff.o(.text.dir_register) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_register) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_register) refers to ff.o(.text.dir_next) for dir_next
    ff.o(.ARM.exidx.text.dir_register) refers to ff.o(.text.dir_register) for [Anonymous Symbol]
    ff.o(.text.remove_chain) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.remove_chain) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.remove_chain) refers to ff.o(.text.remove_chain) for [Anonymous Symbol]
    ff.o(.text.move_window) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.move_window) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.ARM.exidx.text.move_window) refers to ff.o(.text.move_window) for [Anonymous Symbol]
    ff.o(.text.inc_lock) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.ARM.exidx.text.inc_lock) refers to ff.o(.text.inc_lock) for [Anonymous Symbol]
    ff.o(.text.get_fat) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.get_fat) refers to ff.o(.text.get_fat) for [Anonymous Symbol]
    ff.o(.text.f_read) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_read) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_read) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.text.f_read) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_read) refers to ff.o(.text.f_read) for [Anonymous Symbol]
    ff.o(.text.f_write) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_write) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.f_write) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.f_write) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.ARM.exidx.text.f_write) refers to ff.o(.text.f_write) for [Anonymous Symbol]
    ff.o(.text.create_chain) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.create_chain) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.ARM.exidx.text.create_chain) refers to ff.o(.text.create_chain) for [Anonymous Symbol]
    ff.o(.text.f_sync) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_sync) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.f_sync) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_sync) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.f_sync) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.ARM.exidx.text.f_sync) refers to ff.o(.text.f_sync) for [Anonymous Symbol]
    ff.o(.text.sync_fs) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.sync_fs) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.sync_fs) refers to diskio.o(.text.disk_ioctl) for disk_ioctl
    ff.o(.ARM.exidx.text.sync_fs) refers to ff.o(.text.sync_fs) for [Anonymous Symbol]
    ff.o(.text.f_close) refers to ff.o(.text.f_sync) for f_sync
    ff.o(.text.f_close) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_close) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.ARM.exidx.text.f_close) refers to ff.o(.text.f_close) for [Anonymous Symbol]
    ff.o(.text.f_lseek) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_lseek) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_lseek) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.f_lseek) refers to diskio.o(.text.disk_read) for disk_read
    ff.o(.text.f_lseek) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_lseek) refers to ff.o(.text.f_lseek) for [Anonymous Symbol]
    ff.o(.text.f_opendir) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_opendir) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_opendir) refers to ff.o(.text.inc_lock) for inc_lock
    ff.o(.text.f_opendir) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.f_opendir) refers to ff.o(.text.f_opendir) for [Anonymous Symbol]
    ff.o(.text.f_closedir) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_closedir) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.ARM.exidx.text.f_closedir) refers to ff.o(.text.f_closedir) for [Anonymous Symbol]
    ff.o(.text.f_readdir) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_readdir) refers to ff.o(.text.dir_read) for dir_read
    ff.o(.text.f_readdir) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.f_readdir) refers to ff.o(.text.f_readdir) for [Anonymous Symbol]
    ff.o(.text.dir_read) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.dir_read) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.ARM.exidx.text.dir_read) refers to ff.o(.text.dir_read) for [Anonymous Symbol]
    ff.o(.text.dir_next) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.dir_next) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.dir_next) refers to ff.o(.text.sync_window) for sync_window
    ff.o(.text.dir_next) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.ARM.exidx.text.dir_next) refers to ff.o(.text.dir_next) for [Anonymous Symbol]
    ff.o(.text.f_stat) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_stat) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.ARM.exidx.text.f_stat) refers to ff.o(.text.f_stat) for [Anonymous Symbol]
    ff.o(.text.f_getfree) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_getfree) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_getfree) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.f_getfree) refers to ff.o(.text.f_getfree) for [Anonymous Symbol]
    ff.o(.text.f_truncate) refers to diskio.o(.text.disk_status) for disk_status
    ff.o(.text.f_truncate) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_truncate) refers to ff.o(.text.remove_chain) for remove_chain
    ff.o(.text.f_truncate) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_truncate) refers to ff.o(.text.f_truncate) for [Anonymous Symbol]
    ff.o(.text.f_unlink) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_unlink) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_unlink) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_unlink) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.f_unlink) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_unlink) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.f_unlink) refers to ff.o(.text.dir_read) for dir_read
    ff.o(.text.f_unlink) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.ARM.exidx.text.f_unlink) refers to ff.o(.text.f_unlink) for [Anonymous Symbol]
    ff.o(.text.f_mkdir) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_mkdir) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_mkdir) refers to ff.o(.text.create_chain) for create_chain
    ff.o(.text.f_mkdir) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_mkdir) refers to ff.o(.text.get_fat) for get_fat
    ff.o(.text.f_mkdir) refers to ff.o(.text.put_fat) for put_fat
    ff.o(.text.f_mkdir) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.text.f_mkdir) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.f_mkdir) refers to ff.o(.text.dir_register) for dir_register
    ff.o(.text.f_mkdir) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.ARM.exidx.text.f_mkdir) refers to ff.o(.text.f_mkdir) for [Anonymous Symbol]
    ff.o(.text.sync_window) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.sync_window) refers to ff.o(.text.sync_window) for [Anonymous Symbol]
    ff.o(.text.f_rename) refers to ff.o(.text.find_volume) for find_volume
    ff.o(.text.f_rename) refers to ff.o(.text.follow_path) for follow_path
    ff.o(.text.f_rename) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_rename) refers to memcpya.o(.text) for __aeabi_memcpy
    ff.o(.text.f_rename) refers to ff.o(.text.dir_register) for dir_register
    ff.o(.text.f_rename) refers to ff.o(.text.move_window) for move_window
    ff.o(.text.f_rename) refers to ff.o(.text.sync_fs) for sync_fs
    ff.o(.ARM.exidx.text.f_rename) refers to ff.o(.text.f_rename) for [Anonymous Symbol]
    ff.o(.text.f_mkfs) refers to ff.o(.bss..L_MergedGlobals) for .L_MergedGlobals
    ff.o(.text.f_mkfs) refers to diskio.o(.text.disk_initialize) for disk_initialize
    ff.o(.text.f_mkfs) refers to diskio.o(.text.disk_ioctl) for disk_ioctl
    ff.o(.text.f_mkfs) refers to memseta.o(.text) for __aeabi_memclr
    ff.o(.text.f_mkfs) refers to diskio.o(.text.get_fattime) for get_fattime
    ff.o(.text.f_mkfs) refers to diskio.o(.text.disk_write) for disk_write
    ff.o(.ARM.exidx.text.f_mkfs) refers to ff.o(.text.f_mkfs) for [Anonymous Symbol]
    ff.o(.text.put_fat) refers to ff.o(.text.move_window) for move_window
    ff.o(.ARM.exidx.text.put_fat) refers to ff.o(.text.put_fat) for [Anonymous Symbol]
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_initialize) refers to usbh_diskio_dma.o(.text.USBH_initialize) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_status) refers to main.o(.bss.hUSBHost) for hUSBHost
    usbh_diskio_dma.o(.text.USBH_status) refers to usbh_msc.o(.text.USBH_MSC_UnitIsReady) for USBH_MSC_UnitIsReady
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_status) refers to usbh_diskio_dma.o(.text.USBH_status) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_read) refers to main.o(.bss.hUSBHost) for hUSBHost
    usbh_diskio_dma.o(.text.USBH_read) refers to usbh_diskio_dma.o(.bss.scratch) for scratch
    usbh_diskio_dma.o(.text.USBH_read) refers to usbh_msc.o(.text.USBH_MSC_Read) for USBH_MSC_Read
    usbh_diskio_dma.o(.text.USBH_read) refers to memcpya.o(.text) for __aeabi_memcpy
    usbh_diskio_dma.o(.text.USBH_read) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for USBH_MSC_GetLUNInfo
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_read) refers to usbh_diskio_dma.o(.text.USBH_read) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_write) refers to main.o(.bss.hUSBHost) for hUSBHost
    usbh_diskio_dma.o(.text.USBH_write) refers to usbh_diskio_dma.o(.bss.scratch) for scratch
    usbh_diskio_dma.o(.text.USBH_write) refers to memcpya.o(.text) for __aeabi_memcpy
    usbh_diskio_dma.o(.text.USBH_write) refers to usbh_msc.o(.text.USBH_MSC_Write) for USBH_MSC_Write
    usbh_diskio_dma.o(.text.USBH_write) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for USBH_MSC_GetLUNInfo
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_write) refers to usbh_diskio_dma.o(.text.USBH_write) for [Anonymous Symbol]
    usbh_diskio_dma.o(.text.USBH_ioctl) refers to main.o(.bss.hUSBHost) for hUSBHost
    usbh_diskio_dma.o(.text.USBH_ioctl) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for USBH_MSC_GetLUNInfo
    usbh_diskio_dma.o(.ARM.exidx.text.USBH_ioctl) refers to usbh_diskio_dma.o(.text.USBH_ioctl) for [Anonymous Symbol]
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_initialize) for USBH_initialize
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_status) for USBH_status
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_read) for USBH_read
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_write) for USBH_write
    usbh_diskio_dma.o(.rodata.USBH_Driver) refers to usbh_diskio_dma.o(.text.USBH_ioctl) for USBH_ioctl
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_CoreInit) refers to stm32f4xx_ll_usb.o(.text.USB_CoreInit) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetTurnaroundTime) refers to stm32f4xx_ll_usb.o(.text.USB_SetTurnaroundTime) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EnableGlobalInt) refers to stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DisableGlobalInt) refers to stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetCurrentMode) refers to stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetMode) refers to stm32f4xx_ll_usb.o(.text.USB_GetMode) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevInit) refers to stm32f4xx_ll_usb.o(.text.USB_DevInit) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_SetDevSpeed) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushTxFifo) refers to stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushRxFifo) refers to stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetDevSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_GetDevSpeed) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateDedicatedEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateDedicatedEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_DeactivateEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateDedicatedEndpoint) refers to stm32f4xx_ll_usb.o(.text.USB_DeactivateDedicatedEndpoint) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStartXfer) refers to stm32f4xx_ll_usb.o(.text.USB_EPStartXfer) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_WritePacket) refers to stm32f4xx_ll_usb.o(.text.USB_WritePacket) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStopXfer) refers to stm32f4xx_ll_usb.o(.text.USB_EPStopXfer) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadPacket) refers to stm32f4xx_ll_usb.o(.text.USB_ReadPacket) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPSetStall) refers to stm32f4xx_ll_usb.o(.text.USB_EPSetStall) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPClearStall) refers to stm32f4xx_ll_usb.o(.text.USB_EPClearStall) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopDevice) refers to stm32f4xx_ll_usb.o(.text.USB_StopDevice) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevAddress) refers to stm32f4xx_ll_usb.o(.text.USB_SetDevAddress) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevConnect) refers to stm32f4xx_ll_usb.o(.text.USB_DevConnect) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevDisconnect) refers to stm32f4xx_ll_usb.o(.text.USB_DevDisconnect) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadInterrupts) refers to stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadChInterrupts) refers to stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllOutEpInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevAllOutEpInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllInEpInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevAllInEpInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevOutEPInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevOutEPInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevInEPInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_ReadDevInEPInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ClearInterrupts) refers to stm32f4xx_ll_usb.o(.text.USB_ClearInterrupts) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateSetup) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateSetup) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EP0_OutStart) refers to stm32f4xx_ll_usb.o(.text.USB_EP0_OutStart) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HostInit) refers to stm32f4xx_ll_usb.o(.text.USB_HostInit) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_InitFSLSPClkSel) refers to stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.text.USB_ResetPort) refers to stm32f4xx_hal.o(.text.HAL_Delay) for HAL_Delay
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ResetPort) refers to stm32f4xx_ll_usb.o(.text.USB_ResetPort) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DriveVbus) refers to stm32f4xx_ll_usb.o(.text.USB_DriveVbus) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetHostSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetCurrentFrame) refers to stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Init) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Init) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_StartXfer) refers to stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DoPing) refers to stm32f4xx_ll_usb.o(.text.USB_DoPing) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_ReadInterrupt) refers to stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Halt) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Halt) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopHost) refers to stm32f4xx_ll_usb.o(.text.USB_StopHost) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateRemoteWakeup) refers to stm32f4xx_ll_usb.o(.text.USB_ActivateRemoteWakeup) for [Anonymous Symbol]
    stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeActivateRemoteWakeup) refers to stm32f4xx_ll_usb.o(.text.USB_DeActivateRemoteWakeup) for [Anonymous Symbol]
    stm32f4xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_ConfigAnalogFilter) refers to stm32f4xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigAnalogFilter) for [Anonymous Symbol]
    stm32f4xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_ConfigDigitalFilter) refers to stm32f4xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigDigitalFilter) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory) refers to stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLSAI) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLSAI) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLSAI) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLSAI) for [Anonymous Symbol]
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init) refers to stm32f4xx_hal.o(.text.HAL_Init) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(.text.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit) refers to stm32f4xx_hal.o(.text.HAL_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit) refers to stm32f4xx_hal.o(.text.HAL_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit) refers to stm32f4xx_hal.o(.text.HAL_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_IncTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick) refers to stm32f4xx_hal.o(.text.HAL_IncTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTick) refers to stm32f4xx_hal.o(.bss.uwTick) for uwTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.text.HAL_GetTickPrio) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_SetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.text.HAL_GetTickFreq) for [Anonymous Symbol]
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(.text.HAL_Delay) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for .L_MergedGlobals
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay) refers to stm32f4xx_hal.o(.text.HAL_Delay) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick) refers to stm32f4xx_hal.o(.text.HAL_SuspendTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick) refers to stm32f4xx_hal.o(.text.HAL_ResumeTick) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion) refers to stm32f4xx_hal.o(.text.HAL_GetHalVersion) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID) refers to stm32f4xx_hal.o(.text.HAL_GetREVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID) refers to stm32f4xx_hal.o(.text.HAL_GetDEVID) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode) refers to stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_EnableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell) refers to stm32f4xx_hal.o(.text.HAL_DisableCompensationCell) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw0) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw1) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2) refers to stm32f4xx_hal.o(.text.HAL_GetUIDw2) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableMemorySwappingBank) refers to stm32f4xx_hal.o(.text.HAL_EnableMemorySwappingBank) for [Anonymous Symbol]
    stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableMemorySwappingBank) refers to stm32f4xx_hal.o(.text.HAL_DisableMemorySwappingBank) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data..L_MergedGlobals) for uwTickPrio
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.rodata.APBPrescTable) for APBPrescTable
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableOverDrive) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableOverDrive) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableOverDrive) for [Anonymous Symbol]
    stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterUnderDriveSTOPMode) refers to stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterUnderDriveSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(.text.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(.text.UART_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak) refers to stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode) refers to stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver) refers to stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt) for [Anonymous Symbol]
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_sai_ex.o(.ARM.exidx.text.SAI_BlockSynchroConfig) refers to stm32f4xx_hal_sai_ex.o(.text.SAI_BlockSynchroConfig) for [Anonymous Symbol]
    stm32f4xx_hal_sai_ex.o(.ARM.exidx.text.SAI_GetInputClock) refers to stm32f4xx_hal_sai_ex.o(.text.SAI_GetInputClock) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback) refers to stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Init) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.rodata.cst8) for DMA_CalcBaseAndBitshift.flagBitshiftOffset
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError) refers to stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError) for [Anonymous Symbol]
    stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Init) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_MspInit) for HAL_SDRAM_MspInit
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Init) for FMC_SDRAM_Init
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Timing_Init) for FMC_SDRAM_Timing_Init
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Init) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Init) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_MspInit) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DeInit) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_MspDeInit) for HAL_SDRAM_MspDeInit
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_DeInit) for FMC_SDRAM_DeInit
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_DeInit) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_MspDeInit) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_IRQHandler) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_RefreshErrorCallback) for HAL_SDRAM_RefreshErrorCallback
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_IRQHandler) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_RefreshErrorCallback) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_RefreshErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_DMA_XferCpltCallback) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DMA_XferCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_DMA_XferErrorCallback) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DMA_XferErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Read_8b) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_8b) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Write_8b) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_8b) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Read_16b) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_16b) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Write_16b) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_16b) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Read_32b) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_32b) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Write_32b) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_32b) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_DMA) refers to stm32f4xx_hal_sdram.o(.text.SDRAM_DMACpltProt) for SDRAM_DMACpltProt
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_DMA) refers to stm32f4xx_hal_sdram.o(.text.SDRAM_DMACplt) for SDRAM_DMACplt
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_DMA) refers to stm32f4xx_hal_sdram.o(.text.SDRAM_DMAError) for SDRAM_DMAError
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Read_DMA) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.SDRAM_DMACplt) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DMA_XferCpltCallback) for HAL_SDRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.SDRAM_DMACplt) refers to stm32f4xx_hal_sdram.o(.text.SDRAM_DMACplt) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.SDRAM_DMACpltProt) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DMA_XferCpltCallback) for HAL_SDRAM_DMA_XferCpltCallback
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.SDRAM_DMACpltProt) refers to stm32f4xx_hal_sdram.o(.text.SDRAM_DMACpltProt) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.SDRAM_DMAError) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DMA_XferErrorCallback) for HAL_SDRAM_DMA_XferErrorCallback
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.SDRAM_DMAError) refers to stm32f4xx_hal_sdram.o(.text.SDRAM_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_DMA) refers to stm32f4xx_hal_sdram.o(.text.SDRAM_DMACplt) for SDRAM_DMACplt
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_DMA) refers to stm32f4xx_hal_sdram.o(.text.SDRAM_DMAError) for SDRAM_DMAError
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Write_DMA) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_WriteProtection_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Enable) for FMC_SDRAM_WriteProtection_Enable
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_WriteProtection_Enable) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_WriteProtection_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_WriteProtection_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Disable) for FMC_SDRAM_WriteProtection_Disable
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_WriteProtection_Disable) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_WriteProtection_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_SendCommand) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand) for FMC_SDRAM_SendCommand
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_SendCommand) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_SendCommand) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_ProgramRefreshRate) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_ProgramRefreshRate) for FMC_SDRAM_ProgramRefreshRate
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_ProgramRefreshRate) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_ProgramRefreshRate) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_SetAutoRefreshNumber) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SetAutoRefreshNumber) for FMC_SDRAM_SetAutoRefreshNumber
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_SetAutoRefreshNumber) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_SetAutoRefreshNumber) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_GetModeStatus) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_GetModeStatus) for FMC_SDRAM_GetModeStatus
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_GetModeStatus) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_GetModeStatus) for [Anonymous Symbol]
    stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_GetState) refers to stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_InitProtocol) refers to stm32f4xx_hal_sai.o(.rodata.cst16) for .Lswitch.table.HAL_SAI_InitProtocol.5
    stm32f4xx_hal_sai.o(.text.HAL_SAI_InitProtocol) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Init) for HAL_SAI_Init
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_InitProtocol) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_InitProtocol) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Init) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_MspInit) for HAL_SAI_MspInit
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Init) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Init) refers to stm32f4xx_hal_sai_ex.o(.text.SAI_BlockSynchroConfig) for SAI_BlockSynchroConfig
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Init) refers to stm32f4xx_hal_sai_ex.o(.text.SAI_GetInputClock) for SAI_GetInputClock
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Init) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Init) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_MspInit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_DeInit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_sai.o(.text.HAL_SAI_DeInit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_MspDeInit) for HAL_SAI_MspDeInit
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DeInit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_MspDeInit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Transmit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Receive) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_IT) refers to stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT16Bit) for SAI_Transmit_IT16Bit
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_IT) refers to stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT32Bit) for SAI_Transmit_IT32Bit
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_IT) refers to stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT8Bit) for SAI_Transmit_IT8Bit
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Transmit_IT) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_IT) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT8Bit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_TxCpltCallback) for HAL_SAI_TxCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Transmit_IT8Bit) refers to stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT8Bit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT16Bit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_TxCpltCallback) for HAL_SAI_TxCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Transmit_IT16Bit) refers to stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT16Bit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT32Bit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_TxCpltCallback) for HAL_SAI_TxCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Transmit_IT32Bit) refers to stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT32Bit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_IT) refers to stm32f4xx_hal_sai.o(.text.SAI_Receive_IT8Bit) for SAI_Receive_IT8Bit
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_IT) refers to stm32f4xx_hal_sai.o(.text.SAI_Receive_IT16Bit) for SAI_Receive_IT16Bit
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_IT) refers to stm32f4xx_hal_sai.o(.text.SAI_Receive_IT32Bit) for SAI_Receive_IT32Bit
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Receive_IT) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_IT) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_Receive_IT8Bit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_RxCpltCallback) for HAL_SAI_RxCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Receive_IT8Bit) refers to stm32f4xx_hal_sai.o(.text.SAI_Receive_IT8Bit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_Receive_IT16Bit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_RxCpltCallback) for HAL_SAI_RxCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Receive_IT16Bit) refers to stm32f4xx_hal_sai.o(.text.SAI_Receive_IT16Bit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_Receive_IT32Bit) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_RxCpltCallback) for HAL_SAI_RxCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Receive_IT32Bit) refers to stm32f4xx_hal_sai.o(.text.SAI_Receive_IT32Bit) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DMAPause) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_DMAPause) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DMAResume) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_DMAResume) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_DMAStop) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_sai.o(.text.HAL_SAI_DMAStop) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DMAStop) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_DMAStop) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Abort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Abort) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Abort) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Abort) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_DMA) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_DMA) refers to stm32f4xx_hal_sai.o(.text.SAI_DMATxHalfCplt) for SAI_DMATxHalfCplt
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_DMA) refers to stm32f4xx_hal_sai.o(.text.SAI_DMATxCplt) for SAI_DMATxCplt
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_DMA) refers to stm32f4xx_hal_sai.o(.text.SAI_DMAError) for SAI_DMAError
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Transmit_DMA) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_DMATxHalfCplt) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_TxHalfCpltCallback) for HAL_SAI_TxHalfCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMATxHalfCplt) refers to stm32f4xx_hal_sai.o(.text.SAI_DMATxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_DMATxCplt) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_TxCpltCallback) for HAL_SAI_TxCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMATxCplt) refers to stm32f4xx_hal_sai.o(.text.SAI_DMATxCplt) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_DMAError) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_ErrorCallback) for HAL_SAI_ErrorCallback
    stm32f4xx_hal_sai.o(.text.SAI_DMAError) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMAError) refers to stm32f4xx_hal_sai.o(.text.SAI_DMAError) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_DMA) refers to stm32f4xx_hal_sai.o(.text.SAI_DMARxHalfCplt) for SAI_DMARxHalfCplt
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_DMA) refers to stm32f4xx_hal_sai.o(.text.SAI_DMARxCplt) for SAI_DMARxCplt
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_DMA) refers to stm32f4xx_hal_sai.o(.text.SAI_DMAError) for SAI_DMAError
    stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_DMA) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Receive_DMA) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_DMA) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_DMARxHalfCplt) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_RxHalfCpltCallback) for HAL_SAI_RxHalfCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMARxHalfCplt) refers to stm32f4xx_hal_sai.o(.text.SAI_DMARxHalfCplt) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_DMARxCplt) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_RxCpltCallback) for HAL_SAI_RxCpltCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMARxCplt) refers to stm32f4xx_hal_sai.o(.text.SAI_DMARxCplt) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_EnableTxMuteMode) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_EnableTxMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DisableTxMuteMode) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_DisableTxMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_EnableRxMuteMode) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_EnableRxMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DisableRxMuteMode) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_DisableRxMuteMode) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.HAL_SAI_IRQHandler) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_ErrorCallback) for HAL_SAI_ErrorCallback
    stm32f4xx_hal_sai.o(.text.HAL_SAI_IRQHandler) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_Abort) for HAL_SAI_Abort
    stm32f4xx_hal_sai.o(.text.HAL_SAI_IRQHandler) refers to stm32f4xx_hal_sai.o(.text.SAI_DMAAbort) for SAI_DMAAbort
    stm32f4xx_hal_sai.o(.text.HAL_SAI_IRQHandler) refers to stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_IRQHandler) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_ErrorCallback) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_ErrorCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.text.SAI_DMAAbort) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    stm32f4xx_hal_sai.o(.text.SAI_DMAAbort) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_ErrorCallback) for HAL_SAI_ErrorCallback
    stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMAAbort) refers to stm32f4xx_hal_sai.o(.text.SAI_DMAAbort) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_TxCpltCallback) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_TxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_TxHalfCpltCallback) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_TxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_RxCpltCallback) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_RxCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_RxHalfCpltCallback) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_RxHalfCpltCallback) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_GetState) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_GetError) refers to stm32f4xx_hal_sai.o(.text.HAL_SAI_GetError) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Extended_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_CommonSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_CommonSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_AttributeSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_AttributeSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.text.FMC_NAND_GetECC) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_GetECC) refers to stm32f4xx_ll_fmc.o(.text.FMC_NAND_GetECC) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_CommonSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_CommonSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_AttributeSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_AttributeSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_IOSpace_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_IOSpace_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Timing_Init) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Timing_Init) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_DeInit) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_DeInit) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Enable) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Enable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Disable) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Disable) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand) refers to stm32f4xx_hal.o(.text.HAL_GetTick) for HAL_GetTick
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SendCommand) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_ProgramRefreshRate) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_ProgramRefreshRate) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SetAutoRefreshNumber) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SetAutoRefreshNumber) for [Anonymous Symbol]
    stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_GetModeStatus) refers to stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_GetModeStatus) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to usbh_conf.o(.text.HAL_HCD_MspInit) for HAL_HCD_MspInit
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_CoreInit) for USB_CoreInit
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode) for USB_SetCurrentMode
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) refers to stm32f4xx_ll_usb.o(.text.USB_HostInit) for USB_HostInit
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspInit) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for USB_GetHostSpeed
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Init) for USB_HC_Init
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Init) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_ClearHubInfo) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_ClearHubInfo) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Halt) for USB_HC_Halt
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Halt) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) refers to usbh_conf.o(.text.HAL_HCD_MspDeInit) for HAL_HCD_MspDeInit
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) refers to stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt) for USB_DisableGlobalInt
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_DeInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspDeInit) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspDeInit) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest) refers to stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer) for USB_HC_StartXfer
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SubmitRequest) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_GetMode) for USB_GetMode
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts) for USB_ReadInterrupts
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo) for USB_FlushTxFifo
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo) for USB_FlushRxFifo
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel) for USB_InitFSLSPClkSel
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_Disconnect_Callback) for HAL_HCD_Disconnect_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_SOF_Callback) for HAL_HCD_SOF_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt) for USB_HC_ReadInterrupt
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_HC_Halt) for USB_HC_Halt
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts) for USB_ReadChInterrupts
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) for HAL_HCD_HC_NotifyURBChange_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_Connect_Callback) for HAL_HCD_Connect_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback) for HAL_HCD_PortDisabled_Callback
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to stm32f4xx_ll_usb.o(.text.USB_ReadPacket) for USB_ReadPacket
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) refers to usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback) for HAL_HCD_PortEnabled_Callback
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_IRQHandler) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Disconnect_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_SOF_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_SOF_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_WKUP_IRQHandler) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_WKUP_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Connect_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Connect_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortEnabled_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortDisabled_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_NotifyURBChange_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) refers to stm32f4xx_ll_usb.o(.text.USB_DriveVbus) for USB_DriveVbus
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) refers to stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt) for USB_EnableGlobalInt
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Start) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop) refers to stm32f4xx_ll_usb.o(.text.USB_StopHost) for USB_StopHost
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Stop) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort) refers to stm32f4xx_ll_usb.o(.text.USB_ResetPort) for USB_ResetPort
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_ResetPort) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetURBState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetXferCount) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetState) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetState) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame) refers to stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame) for USB_GetCurrentFrame
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentFrame) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for USB_GetHostSpeed
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentSpeed) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed) for [Anonymous Symbol]
    stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SetHubInfo) refers to stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed) for USB_GetHostSpeed
    stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SetHubInfo) refers to stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SetHubInfo) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for __NVIC_SystemReset
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset) refers to stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion) refers to stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent) refers to stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive) refers to stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback) refers to stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend) for [Anonymous Symbol]
    stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend) refers to stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend) for [Anonymous Symbol]
    usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_Reset) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_Reset) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_Reset) for [Anonymous Symbol]
    usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_GetMaxLUN) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN) for [Anonymous Symbol]
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Init) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Init) for [Anonymous Symbol]
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ioreq.o(.text.USBH_BulkSendData) for USBH_BulkSendData
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ctlreq.o(.text.USBH_ClrFeature) for USBH_ClrFeature
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_GetToggle) for USBH_LL_GetToggle
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_SetToggle) for USBH_LL_SetToggle
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ioreq.o(.text.USBH_BulkReceiveData) for USBH_BulkReceiveData
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_GetURBState) for USBH_LL_GetURBState
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_conf.o(.text.USBH_LL_GetLastXferSize) for USBH_LL_GetLastXferSize
    usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) refers to usbh_ctlreq.o(.text.USBH_CtlReq) for USBH_CtlReq
    usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Process) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_core.o(.text.USBH_FindInterface) for USBH_FindInterface
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_core.o(.text.USBH_SelectInterface) for USBH_SelectInterface
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to calloc.o(.text) for calloc
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_pipes.o(.text.USBH_AllocPipe) for USBH_AllocPipe
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Init) for USBH_MSC_BOT_Init
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_pipes.o(.text.USBH_OpenPipe) for USBH_OpenPipe
    usbh_msc.o(.text.USBH_MSC_InterfaceInit) refers to usbh_conf.o(.text.USBH_LL_SetToggle) for USBH_LL_SetToggle
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceInit) refers to usbh_msc.o(.text.USBH_MSC_InterfaceInit) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) refers to usbh_pipes.o(.text.USBH_ClosePipe) for USBH_ClosePipe
    usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) refers to usbh_pipes.o(.text.USBH_FreePipe) for USBH_FreePipe
    usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) refers to malloc.o(i.free) for free
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceDeInit) refers to usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_ClassRequest) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN) for USBH_MSC_BOT_REQ_GetMaxLUN
    usbh_msc.o(.text.USBH_MSC_ClassRequest) refers to usbh_ctlreq.o(.text.USBH_ClrFeature) for USBH_ClrFeature
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_ClassRequest) refers to usbh_msc.o(.text.USBH_MSC_ClassRequest) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity) for USBH_MSC_SCSI_ReadCapacity
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady) for USBH_MSC_SCSI_TestUnitReady
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) for USBH_MSC_SCSI_Inquiry
    usbh_msc.o(.text.USBH_MSC_Process) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) for USBH_MSC_SCSI_RequestSense
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_Process) refers to usbh_msc.o(.text.USBH_MSC_Process) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_SOFProcess) refers to usbh_msc.o(.text.USBH_MSC_SOFProcess) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_IsReady) refers to usbh_msc.o(.text.USBH_MSC_IsReady) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetMaxLUN) refers to usbh_msc.o(.text.USBH_MSC_GetMaxLUN) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_UnitIsReady) refers to usbh_msc.o(.text.USBH_MSC_UnitIsReady) for [Anonymous Symbol]
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetLUNInfo) refers to usbh_msc.o(.text.USBH_MSC_GetLUNInfo) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_Read) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) for USBH_MSC_SCSI_Read
    usbh_msc.o(.text.USBH_MSC_Read) refers to usbh_msc.o(.text.USBH_MSC_RdWrProcess) for USBH_MSC_RdWrProcess
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_Read) refers to usbh_msc.o(.text.USBH_MSC_Read) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_RdWrProcess) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) for USBH_MSC_SCSI_Read
    usbh_msc.o(.text.USBH_MSC_RdWrProcess) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) for USBH_MSC_SCSI_Write
    usbh_msc.o(.text.USBH_MSC_RdWrProcess) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) for USBH_MSC_SCSI_RequestSense
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_RdWrProcess) refers to usbh_msc.o(.text.USBH_MSC_RdWrProcess) for [Anonymous Symbol]
    usbh_msc.o(.text.USBH_MSC_Write) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) for USBH_MSC_SCSI_Write
    usbh_msc.o(.text.USBH_MSC_Write) refers to usbh_msc.o(.text.USBH_MSC_RdWrProcess) for USBH_MSC_RdWrProcess
    usbh_msc.o(.ARM.exidx.text.USBH_MSC_Write) refers to usbh_msc.o(.text.USBH_MSC_Write) for [Anonymous Symbol]
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.rodata.str1.1) for [Anonymous Symbol]
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_InterfaceInit) for USBH_MSC_InterfaceInit
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_InterfaceDeInit) for USBH_MSC_InterfaceDeInit
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_ClassRequest) for USBH_MSC_ClassRequest
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_Process) for USBH_MSC_Process
    usbh_msc.o(.data.USBH_msc) refers to usbh_msc.o(.text.USBH_MSC_SOFProcess) for USBH_MSC_SOFProcess
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_TestUnitReady) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_ReadCapacity) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) refers to memseta.o(.text) for __aeabi_memclr
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Inquiry) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_RequestSense) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Write) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write) for [Anonymous Symbol]
    usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) refers to usbh_msc_bot.o(.text.USBH_MSC_BOT_Process) for USBH_MSC_BOT_Process
    usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Read) refers to usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read) for [Anonymous Symbol]
    system_stm32f4xx.o(.ARM.exidx.text.SystemInit) refers to system_stm32f4xx.o(.text.SystemInit) for [Anonymous Symbol]
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data.SystemCoreClock) for SystemCoreClock
    system_stm32f4xx.o(.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.rodata.AHBPrescTable) for AHBPrescTable
    system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.text.SystemCoreClockUpdate) for [Anonymous Symbol]
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    malloc.o(i.free) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloc.o(i.malloc) refers to mvars.o(.data) for __microlib_freelist
    malloc.o(i.malloc) refers to startup_stm32f429xx.o(HEAP) for __heap_base
    mallocr.o(i.__free$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.__malloc$realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocr.o(i.__malloc$realloc) refers to startup_stm32f429xx.o(HEAP) for __heap_base
    mallocr.o(i.__malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocr.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    mallocr.o(i.realloc) refers to mallocr.o(i.__free$realloc) for __free$realloc
    mallocr.o(i.realloc) refers to mallocr.o(i.internal_alloc) for internal_alloc
    mallocr.o(i.realloc) refers to mallocr.o(i.__malloc$realloc) for __malloc$realloc
    mallocr.o(i.realloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    malloca.o(i.__aligned_malloc) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__aligned_malloc) refers to startup_stm32f429xx.o(HEAP) for __heap_base
    malloca.o(i.__free$memalign) refers to mvars.o(.data) for __microlib_freelist
    malloca.o(i.__malloc$memalign) refers to malloca.o(i.__aligned_malloc) for __aligned_malloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist_initialised
    mallocra.o(i.__aligned_malloc$realloc) refers to startup_stm32f429xx.o(HEAP) for __heap_base
    mallocra.o(i.__aligned_malloc$realloc) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__free$realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.__malloc$realloc$memalign) refers to mallocra.o(i.__aligned_malloc$realloc) for __aligned_malloc$realloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__free$realloc$memalign) for __free$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.internal_alloc) for internal_alloc
    mallocra.o(i.__realloc$memalign) refers to mallocra.o(i.__malloc$realloc$memalign) for __malloc$realloc$memalign
    mallocra.o(i.__realloc$memalign) refers to mvars.o(.data) for __microlib_freelist
    mallocra.o(i.internal_alloc) refers to memcpya.o(.text) for __aeabi_memcpy
    mallocra.o(i.internal_alloc) refers to mvars.o(.data) for __microlib_freelist
    calloc.o(.text) refers to malloc.o(i.malloc) for malloc
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing stm32f4xx_it.o(.text), (0 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.NMI_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.HardFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.MemManage_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.BusFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.UsageFault_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SVC_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.DebugMon_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.PendSV_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing stm32f4xx_it.o(.ARM.exidx.text.OTG_HS_IRQHandler), (8 bytes).
    Removing usbh_conf.o(.text), (0 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspInit), (8 bytes).
    Removing usbh_conf.o(.text.HAL_HCD_MspDeInit), (18 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_MspDeInit), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_SOF_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_Connect_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_Init), (8 bytes).
    Removing usbh_conf.o(.text.USBH_LL_DeInit), (14 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_DeInit), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_Start), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_Stop), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetSpeed), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_ResetPort), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetLastXferSize), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_OpenPipe), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_ClosePipe), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_SubmitURB), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetURBState), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_DriverVBUS), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_SetToggle), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_LL_GetToggle), (8 bytes).
    Removing usbh_conf.o(.ARM.exidx.text.USBH_Delay), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.Custom_LED_Init), (8 bytes).
    Removing main.o(.ARM.exidx.text.Debug_UART_Init), (8 bytes).
    Removing main.o(.ARM.exidx.text.Debug_Printf), (8 bytes).
    Removing main.o(.ARM.exidx.text.USBH_UserProcess), (8 bytes).
    Removing main.o(.ARM.exidx.text.Error_Handler), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing usbh_pipes.o(.text), (0 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_OpenPipe), (8 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_ClosePipe), (8 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_AllocPipe), (8 bytes).
    Removing usbh_pipes.o(.ARM.exidx.text.USBH_FreePipe), (8 bytes).
    Removing usbh_ioreq.o(.text), (0 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendSetup), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_CtlSendData), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_CtlReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_BulkSendData), (8 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_BulkReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_InterruptReceiveData), (38 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_InterruptSendData), (40 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_InterruptSendData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_IsocReceiveData), (40 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_IsocReceiveData), (8 bytes).
    Removing usbh_ioreq.o(.text.USBH_IsocSendData), (40 bytes).
    Removing usbh_ioreq.o(.ARM.exidx.text.USBH_IsocSendData), (8 bytes).
    Removing usbh_core.o(.text), (0 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Init), (8 bytes).
    Removing usbh_core.o(.text.USBH_DeInit), (102 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_DeInit), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_RegisterClass), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_SelectInterface), (8 bytes).
    Removing usbh_core.o(.text.USBH_GetActiveClass), (6 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_GetActiveClass), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_FindInterface), (8 bytes).
    Removing usbh_core.o(.text.USBH_FindInterfaceIndex), (44 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_FindInterfaceIndex), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Start), (8 bytes).
    Removing usbh_core.o(.text.USBH_Stop), (36 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Stop), (8 bytes).
    Removing usbh_core.o(.text.USBH_ReEnumerate), (56 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_ReEnumerate), (8 bytes).
    Removing usbh_core.o(.text.USBH_IsPortEnabled), (6 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_IsPortEnabled), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_Process), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_SetTimer), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_IncTimer), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_PortEnabled), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_PortDisabled), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_Connect), (8 bytes).
    Removing usbh_core.o(.ARM.exidx.text.USBH_LL_Disconnect), (8 bytes).
    Removing usbh_ctlreq.o(.text), (0 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_DevDesc), (8 bytes).
    Removing usbh_ctlreq.o(.text.USBH_GetDescriptor), (58 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_GetDescriptor), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_CfgDesc), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_Get_StringDesc), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_CtlReq), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetAddress), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetCfg), (8 bytes).
    Removing usbh_ctlreq.o(.text.USBH_SetInterface), (28 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetInterface), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_SetFeature), (8 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_ClrFeature), (8 bytes).
    Removing usbh_ctlreq.o(.text.USBH_GetNextDesc), (12 bytes).
    Removing usbh_ctlreq.o(.ARM.exidx.text.USBH_GetNextDesc), (8 bytes).
    Removing ff_gen_drv.o(.text), (0 bytes).
    Removing ff_gen_drv.o(.text.FATFS_LinkDriverEx), (80 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriverEx), (8 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_LinkDriver), (8 bytes).
    Removing ff_gen_drv.o(.text.FATFS_UnLinkDriverEx), (48 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriverEx), (8 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_UnLinkDriver), (8 bytes).
    Removing ff_gen_drv.o(.text.FATFS_GetAttachedDriversNbr), (12 bytes).
    Removing ff_gen_drv.o(.ARM.exidx.text.FATFS_GetAttachedDriversNbr), (8 bytes).
    Removing diskio.o(.text), (0 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_status), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_initialize), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_read), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_write), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.disk_ioctl), (8 bytes).
    Removing diskio.o(.ARM.exidx.text.get_fattime), (8 bytes).
    Removing ff.o(.text), (0 bytes).
    Removing ff.o(.ARM.exidx.text.f_mount), (8 bytes).
    Removing ff.o(.ARM.exidx.text.find_volume), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_open), (8 bytes).
    Removing ff.o(.ARM.exidx.text.follow_path), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_register), (8 bytes).
    Removing ff.o(.text.remove_chain), (160 bytes).
    Removing ff.o(.ARM.exidx.text.remove_chain), (8 bytes).
    Removing ff.o(.ARM.exidx.text.move_window), (8 bytes).
    Removing ff.o(.text.inc_lock), (162 bytes).
    Removing ff.o(.ARM.exidx.text.inc_lock), (8 bytes).
    Removing ff.o(.ARM.exidx.text.get_fat), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_read), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_write), (8 bytes).
    Removing ff.o(.ARM.exidx.text.create_chain), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_sync), (8 bytes).
    Removing ff.o(.ARM.exidx.text.sync_fs), (8 bytes).
    Removing ff.o(.ARM.exidx.text.f_close), (8 bytes).
    Removing ff.o(.text.f_lseek), (740 bytes).
    Removing ff.o(.ARM.exidx.text.f_lseek), (8 bytes).
    Removing ff.o(.text.f_opendir), (274 bytes).
    Removing ff.o(.ARM.exidx.text.f_opendir), (8 bytes).
    Removing ff.o(.text.f_closedir), (104 bytes).
    Removing ff.o(.ARM.exidx.text.f_closedir), (8 bytes).
    Removing ff.o(.text.f_readdir), (540 bytes).
    Removing ff.o(.ARM.exidx.text.f_readdir), (8 bytes).
    Removing ff.o(.text.dir_read), (246 bytes).
    Removing ff.o(.ARM.exidx.text.dir_read), (8 bytes).
    Removing ff.o(.ARM.exidx.text.dir_next), (8 bytes).
    Removing ff.o(.text.f_stat), (222 bytes).
    Removing ff.o(.ARM.exidx.text.f_stat), (8 bytes).
    Removing ff.o(.text.f_getfree), (264 bytes).
    Removing ff.o(.ARM.exidx.text.f_getfree), (8 bytes).
    Removing ff.o(.text.f_truncate), (212 bytes).
    Removing ff.o(.ARM.exidx.text.f_truncate), (8 bytes).
    Removing ff.o(.text.f_unlink), (416 bytes).
    Removing ff.o(.ARM.exidx.text.f_unlink), (8 bytes).
    Removing ff.o(.text.f_mkdir), (682 bytes).
    Removing ff.o(.ARM.exidx.text.f_mkdir), (8 bytes).
    Removing ff.o(.ARM.exidx.text.sync_window), (8 bytes).
    Removing ff.o(.text.f_rename), (576 bytes).
    Removing ff.o(.ARM.exidx.text.f_rename), (8 bytes).
    Removing ff.o(.text.f_mkfs), (2016 bytes).
    Removing ff.o(.ARM.exidx.text.f_mkfs), (8 bytes).
    Removing ff.o(.ARM.exidx.text.put_fat), (8 bytes).
    Removing usbh_diskio_dma.o(.text), (0 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_initialize), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_status), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_read), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_write), (8 bytes).
    Removing usbh_diskio_dma.o(.ARM.exidx.text.USBH_ioctl), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text), (0 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_CoreInit), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_SetTurnaroundTime), (280 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetTurnaroundTime), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EnableGlobalInt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DisableGlobalInt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetCurrentMode), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetMode), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DevInit), (1206 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevInit), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_SetDevSpeed), (16 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevSpeed), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushTxFifo), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_FlushRxFifo), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_GetDevSpeed), (26 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetDevSpeed), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateEndpoint), (130 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateDedicatedEndpoint), (116 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateDedicatedEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DeactivateEndpoint), (130 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DeactivateDedicatedEndpoint), (100 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeactivateDedicatedEndpoint), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPStartXfer), (570 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStartXfer), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_WritePacket), (106 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_WritePacket), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPStopXfer), (270 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPStopXfer), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadPacket), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPSetStall), (62 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPSetStall), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EPClearStall), (82 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EPClearStall), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_StopDevice), (510 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopDevice), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_SetDevAddress), (34 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_SetDevAddress), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DevConnect), (30 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevConnect), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DevDisconnect), (30 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DevDisconnect), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadChInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevAllOutEpInterrupt), (14 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllOutEpInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevAllInEpInterrupt), (14 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevAllInEpInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevOutEPInterrupt), (16 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevOutEPInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ReadDevInEPInterrupt), (36 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ReadDevInEPInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ClearInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ClearInterrupts), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateSetup), (32 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateSetup), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_EP0_OutStart), (90 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_EP0_OutStart), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HostInit), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_InitFSLSPClkSel), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ResetPort), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DriveVbus), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetHostSpeed), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_GetCurrentFrame), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Init), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_StartXfer), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DoPing), (32 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DoPing), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_ReadInterrupt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_HC_Halt), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_StopHost), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_ActivateRemoteWakeup), (24 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_ActivateRemoteWakeup), (8 bytes).
    Removing stm32f4xx_ll_usb.o(.text.USB_DeActivateRemoteWakeup), (18 bytes).
    Removing stm32f4xx_ll_usb.o(.ARM.exidx.text.USB_DeActivateRemoteWakeup), (8 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigAnalogFilter), (60 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_ConfigAnalogFilter), (8 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.text.HAL_I2CEx_ConfigDigitalFilter), (64 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.ARM.exidx.text.HAL_I2CEx_ConfigDigitalFilter), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart), (114 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_MultiBufferStart_IT), (1236 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_MultiBufferStart_IT), (8 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.text.HAL_DMAEx_ChangeMemory), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.ARM.exidx.text.HAL_DMAEx_ChangeMemory), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_PeriphCLKConfig), (576 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_PeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKConfig), (128 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_GetPeriphCLKFreq), (88 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_GetPeriphCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLI2S), (114 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLI2S), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLI2S), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_EnablePLLSAI), (110 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_EnablePLLSAI), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCCEx_DisablePLLSAI), (52 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCCEx_DisablePLLSAI), (8 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.text.HAL_RCC_DeInit), (348 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_ll_sdmmc.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.text), (0 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Init), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_InitTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DeInit), (68 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_IncTick), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickPrio), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SetTickFreq), (38 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetTickFreq), (8 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_Delay), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_SuspendTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_ResumeTick), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetREVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetDEVID), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGSleepMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGSleepMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStopMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStopMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_EnableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_EnableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DBGMCU_DisableDBGStandbyMode), (18 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DBGMCU_DisableDBGStandbyMode), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableCompensationCell), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw0), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw1), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_GetUIDw2), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_EnableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_EnableMemorySwappingBank), (8 bytes).
    Removing stm32f4xx_hal.o(.text.HAL_DisableMemorySwappingBank), (12 bytes).
    Removing stm32f4xx_hal.o(.ARM.exidx.text.HAL_DisableMemorySwappingBank), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text), (0 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DeInit), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_OscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_ClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetSysClockFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_MCOConfig), (168 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_MCOConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_EnableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_DisableCSS), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetHCLKFreq), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetHCLKFreq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK1Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetPCLK2Freq), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetOscConfig), (150 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetOscConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetClockConfig), (62 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_GetClockConfig), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_NMI_IRQHandler), (28 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_NMI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_rcc.o(.text.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(.ARM.exidx.text.HAL_RCC_CSSCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableBkUpReg), (56 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableBkUpReg), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableFlashPowerDown), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_GetVoltageRange), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_ControlVoltageScaling), (194 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_ControlVoltageScaling), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnableOverDrive), (122 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnableOverDrive), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_DisableOverDrive), (118 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_DisableOverDrive), (8 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.text.HAL_PWREx_EnterUnderDriveSTOPMode), (122 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.ARM.exidx.text.HAL_PWREx_EnterUnderDriveSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text), (0 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_SetConfig), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_Init), (104 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_Init), (120 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_Init), (128 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_Init), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DeInit), (54 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive), (558 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_IT), (62 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_IT), (84 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_IT), (50 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit_DMA), (212 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATransmitCplt), (176 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATransmitCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMAError), (380 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Receive_DMA), (384 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Start_Receive_DMA), (336 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Start_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAPause), (342 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAResume), (348 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_DMAStop), (540 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle), (478 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_IT), (200 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_ReceiveToIdle_DMA), (494 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_ReceiveToIdle_DMA), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_GetRxEventType), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_GetRxEventType), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort), (482 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit), (208 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive), (366 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_Abort_IT), (516 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxAbortCallback), (42 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmit_IT), (214 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmit_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMATxOnlyAbortCallback), (16 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMATxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortTransmitCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceive_IT), (372 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxOnlyAbortCallback), (18 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxOnlyAbortCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_AbortReceiveCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_IRQHandler), (1428 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_Receive_IT), (254 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMAAbortOnError), (10 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAAbortOnError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UARTEx_RxEventCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_LIN_SendBreak), (114 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_LIN_SendBreak), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_EnterMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_EnterMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_MultiProcessor_ExitMuteMode), (116 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_MultiProcessor_ExitMuteMode), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableTransmitter), (44 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableTransmitter), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_HalfDuplex_EnableReceiver), (46 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_HalfDuplex_EnableReceiver), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetState), (14 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetState), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.HAL_UART_GetError), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.HAL_UART_GetError), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMAReceiveCplt), (350 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMAReceiveCplt), (8 bytes).
    Removing stm32f4xx_hal_uart.o(.text.UART_DMARxHalfCplt), (24 bytes).
    Removing stm32f4xx_hal_uart.o(.ARM.exidx.text.UART_DMARxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_sai_ex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_sai_ex.o(.text.SAI_BlockSynchroConfig), (32 bytes).
    Removing stm32f4xx_hal_sai_ex.o(.ARM.exidx.text.SAI_BlockSynchroConfig), (8 bytes).
    Removing stm32f4xx_hal_sai_ex.o(.text.SAI_GetInputClock), (198 bytes).
    Removing stm32f4xx_hal_sai_ex.o(.ARM.exidx.text.SAI_GetInputClock), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text), (0 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_Init), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_DeInit), (466 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_DeInit), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_ReadPin), (10 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_ReadPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_WritePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_TogglePin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_TogglePin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_LockPin), (44 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_LockPin), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_IRQHandler), (22 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_gpio.o(.text.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(.ARM.exidx.text.HAL_GPIO_EXTI_Callback), (8 bytes).
    Removing stm32f4xx_hal_spi.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.text), (0 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Init), (354 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Init), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_DeInit), (136 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_DeInit), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start), (114 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Start_IT), (162 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Start_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort), (142 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_Abort_IT), (36 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_PollForTransfer), (454 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_PollForTransfer), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_IRQHandler), (452 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_RegisterCallback), (50 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_RegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_UnRegisterCallback), (156 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_UnRegisterCallback), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetState), (6 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.text.HAL_DMA_GetError), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.ARM.exidx.text.HAL_DMA_GetError), (8 bytes).
    Removing stm32f4xx_hal_dma.o(.rodata.cst8), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text), (0 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Init), (356 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Init), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DeInit), (52 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit), (476 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnFlagUntilTimeout), (458 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnTXEFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnTXEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnBTFFlagUntilTimeout), (186 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnBTFFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive), (796 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnRXNEFlagUntilTimeout), (144 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnRXNEFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit), (378 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_IT), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_IT), (140 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAXferCplt), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAXferCplt), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAError), (76 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Transmit_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Receive_DMA), (276 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write), (348 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryWrite), (200 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryWrite), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read), (600 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_RequestMemoryRead), (274 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_RequestMemoryRead), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_IT), (282 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_IT), (298 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Write_DMA), (514 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Mem_Read_DMA), (576 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Mem_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_IsDeviceReady), (532 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_IsDeviceReady), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_IT), (324 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Transmit_DMA), (496 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_IT), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Seq_Receive_DMA), (598 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Transmit_DMA), (444 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_DMAAbort), (286 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_DMAAbort), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_IT), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Slave_Seq_Receive_DMA), (448 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Slave_Seq_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EnableListen_IT), (54 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EnableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_DisableListen_IT), (64 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_DisableListen_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_Master_Abort_IT), (88 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_Master_Abort_IT), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_ITError), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_ITError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_EV_IRQHandler), (1540 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_EV_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_TXE), (178 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_TXE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterTransmit_BTF), (130 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterTransmit_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MemoryTransmit_TXE_BTF), (190 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MemoryTransmit_TXE_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_RXNE), (318 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_RXNE), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_MasterReceive_BTF), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_MasterReceive_BTF), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ER_IRQHandler), (336 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ER_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MasterRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_SlaveRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AddrCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ListenCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemTxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_MemRxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_AbortCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetState), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetMode), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.HAL_I2C_GetError), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.HAL_I2C_GetError), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (214 bytes).
    Removing stm32f4xx_hal_i2c.o(.ARM.exidx.text.I2C_WaitOnMasterAddressFlagUntilTimeout), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text), (0 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Init), (66 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Init), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_MspInit), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DeInit), (30 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_DeInit), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_IRQHandler), (34 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_RefreshErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_RefreshErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DMA_XferCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_DMA_XferCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_DMA_XferErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_DMA_XferErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_8b), (180 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Read_8b), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_8b), (184 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Write_8b), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_16b), (280 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Read_16b), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_16b), (322 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Write_16b), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_32b), (176 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Read_32b), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_32b), (182 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Write_32b), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Read_DMA), (128 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Read_DMA), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.SDRAM_DMACplt), (22 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.SDRAM_DMACplt), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.SDRAM_DMACpltProt), (22 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.SDRAM_DMACpltProt), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.SDRAM_DMAError), (22 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.SDRAM_DMAError), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_Write_DMA), (116 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_Write_DMA), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_WriteProtection_Enable), (54 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_WriteProtection_Enable), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_WriteProtection_Disable), (50 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_WriteProtection_Disable), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_SendCommand), (68 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_SendCommand), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_ProgramRefreshRate), (58 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_ProgramRefreshRate), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_SetAutoRefreshNumber), (58 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_SetAutoRefreshNumber), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_GetModeStatus), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_GetModeStatus), (8 bytes).
    Removing stm32f4xx_hal_sdram.o(.text.HAL_SDRAM_GetState), (6 bytes).
    Removing stm32f4xx_hal_sdram.o(.ARM.exidx.text.HAL_SDRAM_GetState), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text), (0 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_InitProtocol), (324 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_InitProtocol), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_Init), (476 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Init), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_MspInit), (2 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_MspInit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_DeInit), (138 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DeInit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit), (436 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Transmit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive), (334 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Receive), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_IT), (398 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Transmit_IT), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT8Bit), (102 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Transmit_IT8Bit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT16Bit), (102 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Transmit_IT16Bit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_Transmit_IT32Bit), (102 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Transmit_IT32Bit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_IT), (188 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Receive_IT), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_Receive_IT8Bit), (102 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Receive_IT8Bit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_Receive_IT16Bit), (100 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Receive_IT16Bit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_Receive_IT32Bit), (100 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_Receive_IT32Bit), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_DMAPause), (32 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DMAPause), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_DMAResume), (46 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DMAResume), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_DMAStop), (258 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DMAStop), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_Abort), (272 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Abort), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_Transmit_DMA), (272 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Transmit_DMA), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_DMATxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMATxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_DMATxCplt), (102 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMATxCplt), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_DMAError), (140 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMAError), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_Receive_DMA), (204 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_Receive_DMA), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_DMARxHalfCplt), (6 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMARxHalfCplt), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_DMARxCplt), (102 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMARxCplt), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_EnableTxMuteMode), (36 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_EnableTxMuteMode), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_DisableTxMuteMode), (26 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DisableTxMuteMode), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_EnableRxMuteMode), (58 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_EnableRxMuteMode), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_DisableRxMuteMode), (30 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_DisableRxMuteMode), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_IRQHandler), (350 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_ErrorCallback), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.SAI_DMAAbort), (138 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.SAI_DMAAbort), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_TxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_TxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_TxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_RxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_RxCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_RxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_RxHalfCpltCallback), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_GetState), (6 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_GetState), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.text.HAL_SAI_GetError), (6 bytes).
    Removing stm32f4xx_hal_sai.o(.ARM.exidx.text.HAL_SAI_GetError), (8 bytes).
    Removing stm32f4xx_hal_sai.o(.rodata.cst16), (48 bytes).
    Removing stm32f4xx_ll_fmc.o(.text), (0 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Init), (170 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_DeInit), (48 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Timing_Init), (84 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_Extended_Timing_Init), (80 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_Extended_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Enable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Enable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NORSRAM_WriteOperation_Disable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NORSRAM_WriteOperation_Disable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_Init), (62 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_CommonSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_CommonSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_AttributeSpace_Timing_Init), (40 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_AttributeSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_DeInit), (70 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Enable), (20 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Enable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_ECC_Disable), (20 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_ECC_Disable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_NAND_GetECC), (128 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_NAND_GetECC), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_Init), (44 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_CommonSpace_Timing_Init), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_CommonSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_AttributeSpace_Timing_Init), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_AttributeSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_IOSpace_Timing_Init), (30 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_IOSpace_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_PCCARD_DeInit), (32 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_PCCARD_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Init), (126 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_Timing_Init), (216 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_Timing_Init), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_DeInit), (32 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_DeInit), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Enable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Enable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_WriteProtection_Disable), (18 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_WriteProtection_Disable), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SendCommand), (108 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SendCommand), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_ProgramRefreshRate), (20 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_ProgramRefreshRate), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_SetAutoRefreshNumber), (24 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_SetAutoRefreshNumber), (8 bytes).
    Removing stm32f4xx_ll_fmc.o(.text.FMC_SDRAM_GetModeStatus), (14 bytes).
    Removing stm32f4xx_ll_fmc.o(.ARM.exidx.text.FMC_SDRAM_GetModeStatus), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text), (0 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Init), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspInit), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspInit), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Init), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_ClearHubInfo), (18 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_ClearHubInfo), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_Halt), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_DeInit), (36 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_DeInit), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_MspDeInit), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SubmitRequest), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_Disconnect_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Disconnect_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_SOF_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_SOF_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_WKUP_IRQHandler), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_WKUP_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_Connect_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Connect_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortEnabled_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortEnabled_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_PortDisabled_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_PortDisabled_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_NotifyURBChange_Callback), (2 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_NotifyURBChange_Callback), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Start), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_Stop), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_ResetPort), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetState), (6 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetState), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetURBState), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetXferCount), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetState), (14 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_GetState), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentFrame), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_GetCurrentSpeed), (8 bytes).
    Removing stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SetHubInfo), (64 bytes).
    Removing stm32f4xx_hal_hcd.o(.ARM.exidx.text.HAL_HCD_HC_SetHubInfo), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text), (0 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_EnableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_DisableIRQ), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_DisableIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SystemReset), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.__NVIC_SystemReset), (42 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.__NVIC_SystemReset), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Config), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Disable), (26 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Disable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_Enable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_Enable), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_EnableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_EnableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_DisableRegion), (22 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_DisableRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_MPU_ConfigRegion), (94 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_MPU_ConfigRegion), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_CORTEX_ClearEvent), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriorityGrouping), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPriority), (88 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPriority), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_SetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetPendingIRQ), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_ClearPendingIRQ), (34 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_ClearPendingIRQ), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_NVIC_GetActive), (38 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_NVIC_GetActive), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_CLKSourceConfig), (24 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_CLKSourceConfig), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_IRQHandler), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(.ARM.exidx.text.HAL_SYSTICK_Callback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text), (0 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DeInit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DeInit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableBkUpAccess), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableBkUpAccess), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_ConfigPVD), (130 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_ConfigPVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisablePVD), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableWakeUpPin), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableWakeUpPin), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSLEEPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTOPMode), (84 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTOPMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnterSTANDBYMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnterSTANDBYMode), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVD_IRQHandler), (30 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVD_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_PVDCallback), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSleepOnExit), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSleepOnExit), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_EnableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_EnableSEVOnPend), (8 bytes).
    Removing stm32f4xx_hal_pwr.o(.text.HAL_PWR_DisableSEVOnPend), (18 bytes).
    Removing stm32f4xx_hal_pwr.o(.ARM.exidx.text.HAL_PWR_DisableSEVOnPend), (8 bytes).
    Removing usbh_msc_bot.o(.text), (0 bytes).
    Removing usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_Reset), (22 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_Reset), (8 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_REQ_GetMaxLUN), (8 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Init), (8 bytes).
    Removing usbh_msc_bot.o(.ARM.exidx.text.USBH_MSC_BOT_Process), (8 bytes).
    Removing usbh_msc.o(.text), (0 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceInit), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_InterfaceDeInit), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_ClassRequest), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_Process), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_SOFProcess), (8 bytes).
    Removing usbh_msc.o(.text.USBH_MSC_IsReady), (26 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_IsReady), (8 bytes).
    Removing usbh_msc.o(.text.USBH_MSC_GetMaxLUN), (26 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetMaxLUN), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_UnitIsReady), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_GetLUNInfo), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_Read), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_RdWrProcess), (8 bytes).
    Removing usbh_msc.o(.ARM.exidx.text.USBH_MSC_Write), (8 bytes).
    Removing usbh_msc_scsi.o(.text), (0 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_TestUnitReady), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_ReadCapacity), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Inquiry), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_RequestSense), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Write), (8 bytes).
    Removing usbh_msc_scsi.o(.ARM.exidx.text.USBH_MSC_SCSI_Read), (8 bytes).
    Removing system_stm32f4xx.o(.text), (0 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemInit), (8 bytes).
    Removing system_stm32f4xx.o(.text.SystemCoreClockUpdate), (134 bytes).
    Removing system_stm32f4xx.o(.ARM.exidx.text.SystemCoreClockUpdate), (8 bytes).

999 unused section(s) (total 60270 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/malloc/calloc.c         0x00000000   Number         0  calloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocr.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  malloca.o ABSOLUTE
    ../clib/microlib/malloc/malloc.c         0x00000000   Number         0  mallocra.o ABSOLUTE
    ../clib/microlib/malloc/mvars.c          0x00000000   Number         0  mvars.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/string/strlen.c         0x00000000   Number         0  strlen.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    diskio.c                                 0x00000000   Number         0  diskio.o ABSOLUTE
    ff.c                                     0x00000000   Number         0  ff.o ABSOLUTE
    ff_gen_drv.c                             0x00000000   Number         0  ff_gen_drv.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    startup_stm32f429xx.s                    0x00000000   Number         0  startup_stm32f429xx.o ABSOLUTE
    stm32f4xx_hal.c                          0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    stm32f4xx_hal_cortex.c                   0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    stm32f4xx_hal_dma.c                      0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    stm32f4xx_hal_dma_ex.c                   0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    stm32f4xx_hal_gpio.c                     0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    stm32f4xx_hal_hcd.c                      0x00000000   Number         0  stm32f4xx_hal_hcd.o ABSOLUTE
    stm32f4xx_hal_i2c.c                      0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    stm32f4xx_hal_i2c_ex.c                   0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    stm32f4xx_hal_pwr.c                      0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    stm32f4xx_hal_pwr_ex.c                   0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    stm32f4xx_hal_rcc.c                      0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    stm32f4xx_hal_rcc_ex.c                   0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    stm32f4xx_hal_sai.c                      0x00000000   Number         0  stm32f4xx_hal_sai.o ABSOLUTE
    stm32f4xx_hal_sai_ex.c                   0x00000000   Number         0  stm32f4xx_hal_sai_ex.o ABSOLUTE
    stm32f4xx_hal_sdram.c                    0x00000000   Number         0  stm32f4xx_hal_sdram.o ABSOLUTE
    stm32f4xx_hal_spi.c                      0x00000000   Number         0  stm32f4xx_hal_spi.o ABSOLUTE
    stm32f4xx_hal_uart.c                     0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_ll_fmc.c                       0x00000000   Number         0  stm32f4xx_ll_fmc.o ABSOLUTE
    stm32f4xx_ll_sdmmc.c                     0x00000000   Number         0  stm32f4xx_ll_sdmmc.o ABSOLUTE
    stm32f4xx_ll_usb.c                       0x00000000   Number         0  stm32f4xx_ll_usb.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    usbh_conf.c                              0x00000000   Number         0  usbh_conf.o ABSOLUTE
    usbh_core.c                              0x00000000   Number         0  usbh_core.o ABSOLUTE
    usbh_ctlreq.c                            0x00000000   Number         0  usbh_ctlreq.o ABSOLUTE
    usbh_diskio_dma.c                        0x00000000   Number         0  usbh_diskio_dma.o ABSOLUTE
    usbh_ioreq.c                             0x00000000   Number         0  usbh_ioreq.o ABSOLUTE
    usbh_msc.c                               0x00000000   Number         0  usbh_msc.o ABSOLUTE
    usbh_msc_bot.c                           0x00000000   Number         0  usbh_msc_bot.o ABSOLUTE
    usbh_msc_scsi.c                          0x00000000   Number         0  usbh_msc_scsi.o ABSOLUTE
    usbh_pipes.c                             0x00000000   Number         0  usbh_pipes.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x080001bc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x080001bc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001bc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001bc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    $v0                                      0x080001c0   Number         0  startup_stm32f429xx.o(.text)
    .text                                    0x080001c0   Section       36  startup_stm32f429xx.o(.text)
    .text                                    0x080001e4   Section        0  uldiv.o(.text)
    .text                                    0x08000246   Section        0  memcpya.o(.text)
    .text                                    0x0800026a   Section        0  memseta.o(.text)
    .text                                    0x0800028e   Section        0  strlen.o(.text)
    .text                                    0x0800029c   Section        0  calloc.o(.text)
    .text                                    0x080002b8   Section        0  uidiv.o(.text)
    .text                                    0x080002e4   Section        0  llshl.o(.text)
    .text                                    0x08000302   Section        0  llushr.o(.text)
    .text                                    0x08000322   Section        0  iusefp.o(.text)
    .text                                    0x08000322   Section        0  dadd.o(.text)
    .text                                    0x08000470   Section        0  dmul.o(.text)
    .text                                    0x08000554   Section        0  ddiv.o(.text)
    .text                                    0x08000632   Section        0  dfixul.o(.text)
    .text                                    0x08000664   Section       48  cdrcmple.o(.text)
    .text                                    0x08000694   Section       48  init.o(.text)
    .text                                    0x080006c4   Section        0  llsshr.o(.text)
    .text                                    0x080006e8   Section        0  depilogue.o(.text)
    [Anonymous Symbol]                       0x080007a4   Section        0  stm32f4xx_it.o(.text.BusFault_Handler)
    [Anonymous Symbol]                       0x080007a8   Section        0  main.o(.text.Custom_LED_Init)
    [Anonymous Symbol]                       0x08000860   Section        0  stm32f4xx_it.o(.text.DebugMon_Handler)
    [Anonymous Symbol]                       0x08000864   Section        0  main.o(.text.Debug_Printf)
    [Anonymous Symbol]                       0x080008a8   Section        0  main.o(.text.Debug_UART_Init)
    Error_Handler                            0x08000979   Thumb Code    42  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x08000978   Section        0  main.o(.text.Error_Handler)
    [Anonymous Symbol]                       0x080009a4   Section        0  ff_gen_drv.o(.text.FATFS_LinkDriver)
    [Anonymous Symbol]                       0x080009f0   Section        0  ff_gen_drv.o(.text.FATFS_UnLinkDriver)
    [Anonymous Symbol]                       0x08000a20   Section        0  stm32f4xx_hal.o(.text.HAL_Delay)
    [Anonymous Symbol]                       0x08000a48   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    [Anonymous Symbol]                       0x08000be8   Section        0  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    [Anonymous Symbol]                       0x08000bf4   Section        0  stm32f4xx_hal.o(.text.HAL_GetTick)
    [Anonymous Symbol]                       0x08000c00   Section        0  usbh_conf.o(.text.HAL_HCD_Connect_Callback)
    [Anonymous Symbol]                       0x08000c08   Section        0  usbh_conf.o(.text.HAL_HCD_Disconnect_Callback)
    [Anonymous Symbol]                       0x08000c10   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame)
    [Anonymous Symbol]                       0x08000c18   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed)
    [Anonymous Symbol]                       0x08000c20   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState)
    [Anonymous Symbol]                       0x08000c30   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount)
    [Anonymous Symbol]                       0x08000c3c   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt)
    [Anonymous Symbol]                       0x08000c64   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init)
    [Anonymous Symbol]                       0x08000d0c   Section        0  usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback)
    [Anonymous Symbol]                       0x08000d10   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest)
    [Anonymous Symbol]                       0x08000db4   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler)
    [Anonymous Symbol]                       0x08001704   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init)
    [Anonymous Symbol]                       0x08001790   Section        0  usbh_conf.o(.text.HAL_HCD_MspInit)
    [Anonymous Symbol]                       0x08001824   Section        0  usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback)
    [Anonymous Symbol]                       0x0800182c   Section        0  usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback)
    [Anonymous Symbol]                       0x08001834   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort)
    [Anonymous Symbol]                       0x0800183c   Section        0  usbh_conf.o(.text.HAL_HCD_SOF_Callback)
    [Anonymous Symbol]                       0x08001844   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start)
    [Anonymous Symbol]                       0x08001874   Section        0  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop)
    [Anonymous Symbol]                       0x0800189c   Section        0  stm32f4xx_hal.o(.text.HAL_IncTick)
    [Anonymous Symbol]                       0x080018b8   Section        0  stm32f4xx_hal.o(.text.HAL_Init)
    [Anonymous Symbol]                       0x080018f0   Section        0  stm32f4xx_hal.o(.text.HAL_InitTick)
    [Anonymous Symbol]                       0x08001938   Section        0  stm32f4xx_hal.o(.text.HAL_MspInit)
    [Anonymous Symbol]                       0x0800193c   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x08001960   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    [Anonymous Symbol]                       0x080019b8   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    [Anonymous Symbol]                       0x080019d8   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    [Anonymous Symbol]                       0x08001b3c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    [Anonymous Symbol]                       0x08001b64   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    [Anonymous Symbol]                       0x08001b8c   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    [Anonymous Symbol]                       0x08001bfc   Section        0  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    [Anonymous Symbol]                       0x08001fa8   Section        0  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    [Anonymous Symbol]                       0x08001fd4   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    [Anonymous Symbol]                       0x08002034   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit)
    [Anonymous Symbol]                       0x08002038   Section        0  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    [Anonymous Symbol]                       0x080021cc   Section        0  stm32f4xx_it.o(.text.HardFault_Handler)
    [Anonymous Symbol]                       0x080021d0   Section        0  stm32f4xx_it.o(.text.MemManage_Handler)
    [Anonymous Symbol]                       0x080021d4   Section        0  stm32f4xx_it.o(.text.NMI_Handler)
    [Anonymous Symbol]                       0x080021d8   Section        0  stm32f4xx_it.o(.text.OTG_HS_IRQHandler)
    [Anonymous Symbol]                       0x080021e4   Section        0  stm32f4xx_it.o(.text.PendSV_Handler)
    [Anonymous Symbol]                       0x080021e8   Section        0  stm32f4xx_it.o(.text.SVC_Handler)
    [Anonymous Symbol]                       0x080021ec   Section        0  stm32f4xx_it.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x080021f0   Section        0  system_stm32f4xx.o(.text.SystemInit)
    UART_SetConfig                           0x08002249   Thumb Code   230  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08002248   Section        0  stm32f4xx_hal_uart.o(.text.UART_SetConfig)
    [Anonymous Symbol]                       0x08002330   Section        0  usbh_pipes.o(.text.USBH_AllocPipe)
    [Anonymous Symbol]                       0x08002404   Section        0  usbh_ioreq.o(.text.USBH_BulkReceiveData)
    [Anonymous Symbol]                       0x0800242c   Section        0  usbh_ioreq.o(.text.USBH_BulkSendData)
    [Anonymous Symbol]                       0x08002460   Section        0  usbh_pipes.o(.text.USBH_ClosePipe)
    [Anonymous Symbol]                       0x0800246c   Section        0  usbh_ctlreq.o(.text.USBH_ClrFeature)
    [Anonymous Symbol]                       0x08002488   Section        0  usbh_ioreq.o(.text.USBH_CtlReceiveData)
    [Anonymous Symbol]                       0x080024b0   Section        0  usbh_ctlreq.o(.text.USBH_CtlReq)
    [Anonymous Symbol]                       0x08002694   Section        0  usbh_ioreq.o(.text.USBH_CtlSendData)
    [Anonymous Symbol]                       0x080026c8   Section        0  usbh_ioreq.o(.text.USBH_CtlSendSetup)
    [Anonymous Symbol]                       0x080026ec   Section        0  usbh_conf.o(.text.USBH_Delay)
    [Anonymous Symbol]                       0x080026f0   Section        0  usbh_core.o(.text.USBH_FindInterface)
    [Anonymous Symbol]                       0x080027ec   Section        0  usbh_pipes.o(.text.USBH_FreePipe)
    [Anonymous Symbol]                       0x08002804   Section        0  usbh_ctlreq.o(.text.USBH_Get_CfgDesc)
    [Anonymous Symbol]                       0x08002a94   Section        0  usbh_ctlreq.o(.text.USBH_Get_DevDesc)
    [Anonymous Symbol]                       0x08002b50   Section        0  usbh_ctlreq.o(.text.USBH_Get_StringDesc)
    [Anonymous Symbol]                       0x08002c0c   Section        0  usbh_core.o(.text.USBH_Init)
    [Anonymous Symbol]                       0x08002c84   Section        0  usbh_conf.o(.text.USBH_LL_ClosePipe)
    [Anonymous Symbol]                       0x08002c94   Section        0  usbh_core.o(.text.USBH_LL_Connect)
    [Anonymous Symbol]                       0x08002cac   Section        0  usbh_core.o(.text.USBH_LL_Disconnect)
    [Anonymous Symbol]                       0x08002cdc   Section        0  usbh_conf.o(.text.USBH_LL_DriverVBUS)
    [Anonymous Symbol]                       0x08002d00   Section        0  usbh_conf.o(.text.USBH_LL_GetLastXferSize)
    [Anonymous Symbol]                       0x08002d08   Section        0  usbh_conf.o(.text.USBH_LL_GetSpeed)
    [Anonymous Symbol]                       0x08002d1c   Section        0  usbh_conf.o(.text.USBH_LL_GetToggle)
    [Anonymous Symbol]                       0x08002d3c   Section        0  usbh_conf.o(.text.USBH_LL_GetURBState)
    [Anonymous Symbol]                       0x08002d44   Section        0  usbh_core.o(.text.USBH_LL_IncTimer)
    [Anonymous Symbol]                       0x08002d64   Section        0  usbh_conf.o(.text.USBH_LL_Init)
    [Anonymous Symbol]                       0x08002dac   Section        0  usbh_conf.o(.text.USBH_LL_OpenPipe)
    [Anonymous Symbol]                       0x08002dcc   Section        0  usbh_core.o(.text.USBH_LL_PortDisabled)
    [Anonymous Symbol]                       0x08002ddc   Section        0  usbh_core.o(.text.USBH_LL_PortEnabled)
    [Anonymous Symbol]                       0x08002de4   Section        0  usbh_conf.o(.text.USBH_LL_ResetPort)
    [Anonymous Symbol]                       0x08002df4   Section        0  usbh_core.o(.text.USBH_LL_SetTimer)
    [Anonymous Symbol]                       0x08002dfc   Section        0  usbh_conf.o(.text.USBH_LL_SetToggle)
    [Anonymous Symbol]                       0x08002e20   Section        0  usbh_conf.o(.text.USBH_LL_Start)
    [Anonymous Symbol]                       0x08002e30   Section        0  usbh_conf.o(.text.USBH_LL_Stop)
    [Anonymous Symbol]                       0x08002e40   Section        0  usbh_conf.o(.text.USBH_LL_SubmitURB)
    [Anonymous Symbol]                       0x08002e64   Section        0  usbh_msc_bot.o(.text.USBH_MSC_BOT_Init)
    [Anonymous Symbol]                       0x08002e8c   Section        0  usbh_msc_bot.o(.text.USBH_MSC_BOT_Process)
    [Anonymous Symbol]                       0x080030c4   Section        0  usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN)
    USBH_MSC_ClassRequest                    0x080030dd   Thumb Code   120  usbh_msc.o(.text.USBH_MSC_ClassRequest)
    [Anonymous Symbol]                       0x080030dc   Section        0  usbh_msc.o(.text.USBH_MSC_ClassRequest)
    [Anonymous Symbol]                       0x08003154   Section        0  usbh_msc.o(.text.USBH_MSC_GetLUNInfo)
    USBH_MSC_InterfaceDeInit                 0x080031ad   Thumb Code    78  usbh_msc.o(.text.USBH_MSC_InterfaceDeInit)
    [Anonymous Symbol]                       0x080031ac   Section        0  usbh_msc.o(.text.USBH_MSC_InterfaceDeInit)
    USBH_MSC_InterfaceInit                   0x080031fd   Thumb Code   270  usbh_msc.o(.text.USBH_MSC_InterfaceInit)
    [Anonymous Symbol]                       0x080031fc   Section        0  usbh_msc.o(.text.USBH_MSC_InterfaceInit)
    USBH_MSC_Process                         0x0800330d   Thumb Code   514  usbh_msc.o(.text.USBH_MSC_Process)
    [Anonymous Symbol]                       0x0800330c   Section        0  usbh_msc.o(.text.USBH_MSC_Process)
    USBH_MSC_RdWrProcess                     0x08003511   Thumb Code   142  usbh_msc.o(.text.USBH_MSC_RdWrProcess)
    [Anonymous Symbol]                       0x08003510   Section        0  usbh_msc.o(.text.USBH_MSC_RdWrProcess)
    [Anonymous Symbol]                       0x080035a0   Section        0  usbh_msc.o(.text.USBH_MSC_Read)
    [Anonymous Symbol]                       0x08003614   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry)
    [Anonymous Symbol]                       0x080036c4   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read)
    [Anonymous Symbol]                       0x08003750   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity)
    [Anonymous Symbol]                       0x080037c0   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense)
    [Anonymous Symbol]                       0x0800384c   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady)
    [Anonymous Symbol]                       0x08003894   Section        0  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write)
    USBH_MSC_SOFProcess                      0x08003921   Thumb Code     4  usbh_msc.o(.text.USBH_MSC_SOFProcess)
    [Anonymous Symbol]                       0x08003920   Section        0  usbh_msc.o(.text.USBH_MSC_SOFProcess)
    [Anonymous Symbol]                       0x08003924   Section        0  usbh_msc.o(.text.USBH_MSC_UnitIsReady)
    [Anonymous Symbol]                       0x0800394c   Section        0  usbh_msc.o(.text.USBH_MSC_Write)
    [Anonymous Symbol]                       0x080039c0   Section        0  usbh_pipes.o(.text.USBH_OpenPipe)
    [Anonymous Symbol]                       0x080039dc   Section        0  usbh_core.o(.text.USBH_Process)
    [Anonymous Symbol]                       0x08003e88   Section        0  usbh_core.o(.text.USBH_RegisterClass)
    [Anonymous Symbol]                       0x08003ea4   Section        0  usbh_core.o(.text.USBH_SelectInterface)
    [Anonymous Symbol]                       0x08003eb8   Section        0  usbh_ctlreq.o(.text.USBH_SetAddress)
    [Anonymous Symbol]                       0x08003ed4   Section        0  usbh_ctlreq.o(.text.USBH_SetCfg)
    [Anonymous Symbol]                       0x08003ef0   Section        0  usbh_ctlreq.o(.text.USBH_SetFeature)
    [Anonymous Symbol]                       0x08003f0c   Section        0  usbh_core.o(.text.USBH_Start)
    USBH_UserProcess                         0x08003f21   Thumb Code   120  main.o(.text.USBH_UserProcess)
    [Anonymous Symbol]                       0x08003f20   Section        0  main.o(.text.USBH_UserProcess)
    [Anonymous Symbol]                       0x08004008   Section        0  usbh_diskio_dma.o(.text.USBH_initialize)
    [Anonymous Symbol]                       0x0800400c   Section        0  usbh_diskio_dma.o(.text.USBH_ioctl)
    [Anonymous Symbol]                       0x08004090   Section        0  usbh_diskio_dma.o(.text.USBH_read)
    [Anonymous Symbol]                       0x0800414c   Section        0  usbh_diskio_dma.o(.text.USBH_status)
    [Anonymous Symbol]                       0x08004164   Section        0  usbh_diskio_dma.o(.text.USBH_write)
    [Anonymous Symbol]                       0x08004230   Section        0  stm32f4xx_ll_usb.o(.text.USB_CoreInit)
    [Anonymous Symbol]                       0x080044a8   Section        0  stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt)
    [Anonymous Symbol]                       0x080044b8   Section        0  stm32f4xx_ll_usb.o(.text.USB_DriveVbus)
    [Anonymous Symbol]                       0x08004504   Section        0  stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt)
    [Anonymous Symbol]                       0x08004514   Section        0  stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo)
    [Anonymous Symbol]                       0x080045d4   Section        0  stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo)
    [Anonymous Symbol]                       0x08004694   Section        0  stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame)
    [Anonymous Symbol]                       0x0800469c   Section        0  stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed)
    [Anonymous Symbol]                       0x080046b4   Section        0  stm32f4xx_ll_usb.o(.text.USB_GetMode)
    [Anonymous Symbol]                       0x080046bc   Section        0  stm32f4xx_ll_usb.o(.text.USB_HC_Halt)
    [Anonymous Symbol]                       0x08004840   Section        0  stm32f4xx_ll_usb.o(.text.USB_HC_Init)
    [Anonymous Symbol]                       0x08004968   Section        0  stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt)
    [Anonymous Symbol]                       0x08004970   Section        0  stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer)
    [Anonymous Symbol]                       0x08004c68   Section        0  stm32f4xx_ll_usb.o(.text.USB_HostInit)
    [Anonymous Symbol]                       0x08004f48   Section        0  stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel)
    [Anonymous Symbol]                       0x08004f88   Section        0  stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts)
    [Anonymous Symbol]                       0x08004f98   Section        0  stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts)
    [Anonymous Symbol]                       0x08004fa0   Section        0  stm32f4xx_ll_usb.o(.text.USB_ReadPacket)
    [Anonymous Symbol]                       0x0800504c   Section        0  stm32f4xx_ll_usb.o(.text.USB_ResetPort)
    [Anonymous Symbol]                       0x0800508c   Section        0  stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode)
    [Anonymous Symbol]                       0x080053f4   Section        0  stm32f4xx_ll_usb.o(.text.USB_StopHost)
    [Anonymous Symbol]                       0x08005d2c   Section        0  stm32f4xx_it.o(.text.UsageFault_Handler)
    create_chain                             0x08005d31   Thumb Code   286  ff.o(.text.create_chain)
    [Anonymous Symbol]                       0x08005d30   Section        0  ff.o(.text.create_chain)
    dir_next                                 0x08005e51   Thumb Code   350  ff.o(.text.dir_next)
    [Anonymous Symbol]                       0x08005e50   Section        0  ff.o(.text.dir_next)
    dir_register                             0x08005fb1   Thumb Code   288  ff.o(.text.dir_register)
    [Anonymous Symbol]                       0x08005fb0   Section        0  ff.o(.text.dir_register)
    [Anonymous Symbol]                       0x080060d0   Section        0  diskio.o(.text.disk_initialize)
    [Anonymous Symbol]                       0x08006104   Section        0  diskio.o(.text.disk_ioctl)
    [Anonymous Symbol]                       0x0800611c   Section        0  diskio.o(.text.disk_read)
    [Anonymous Symbol]                       0x0800613c   Section        0  diskio.o(.text.disk_status)
    [Anonymous Symbol]                       0x08006154   Section        0  diskio.o(.text.disk_write)
    [Anonymous Symbol]                       0x08006174   Section        0  ff.o(.text.f_close)
    [Anonymous Symbol]                       0x080061e0   Section        0  ff.o(.text.f_mount)
    [Anonymous Symbol]                       0x080062f8   Section        0  ff.o(.text.f_open)
    [Anonymous Symbol]                       0x08006698   Section        0  ff.o(.text.f_read)
    [Anonymous Symbol]                       0x080069a0   Section        0  ff.o(.text.f_sync)
    [Anonymous Symbol]                       0x08006a60   Section        0  ff.o(.text.f_write)
    find_volume                              0x08006da9   Thumb Code  1342  ff.o(.text.find_volume)
    [Anonymous Symbol]                       0x08006da8   Section        0  ff.o(.text.find_volume)
    follow_path                              0x080072e9   Thumb Code  1640  ff.o(.text.follow_path)
    [Anonymous Symbol]                       0x080072e8   Section        0  ff.o(.text.follow_path)
    get_fat                                  0x08007951   Thumb Code   200  ff.o(.text.get_fat)
    [Anonymous Symbol]                       0x08007950   Section        0  ff.o(.text.get_fat)
    [Anonymous Symbol]                       0x08007a18   Section        0  diskio.o(.text.get_fattime)
    [Anonymous Symbol]                       0x08007a1c   Section        0  main.o(.text.main)
    move_window                              0x08007e91   Thumb Code   124  ff.o(.text.move_window)
    [Anonymous Symbol]                       0x08007e90   Section        0  ff.o(.text.move_window)
    put_fat                                  0x08007f0d   Thumb Code   280  ff.o(.text.put_fat)
    [Anonymous Symbol]                       0x08007f0c   Section        0  ff.o(.text.put_fat)
    sync_fs                                  0x08008025   Thumb Code   204  ff.o(.text.sync_fs)
    [Anonymous Symbol]                       0x08008024   Section        0  ff.o(.text.sync_fs)
    sync_window                              0x080080f1   Thumb Code    90  ff.o(.text.sync_window)
    [Anonymous Symbol]                       0x080080f0   Section        0  ff.o(.text.sync_window)
    i.__0vsnprintf                           0x0800814c   Section        0  printfa.o(i.__0vsnprintf)
    i.__scatterload_copy                     0x08008180   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800818e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08008190   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x080081a1   Thumb Code   366  printfa.o(i._fp_digits)
    i._fp_digits                             0x080081a0   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x08008325   Thumb Code  1744  printfa.o(i._printf_core)
    i._printf_core                           0x08008324   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x08008a01   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x08008a00   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x08008a25   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x08008a24   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x08008a53   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x08008a52   Section        0  printfa.o(i._snputc)
    i.free                                   0x08008a68   Section        0  malloc.o(i.free)
    i.malloc                                 0x08008ab8   Section        0  malloc.o(i.malloc)
    ExCvt                                    0x08008b3c   Data         128  ff.o(.rodata.ExCvt)
    [Anonymous Symbol]                       0x08008b3c   Section        0  ff.o(.rodata.ExCvt)
    .L.str.26                                0x08008bd0   Data          28  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08008bd0   Section        0  main.o(.rodata.str1.1)
    .L.str.17                                0x08008bec   Data          42  main.o(.rodata.str1.1)
    .L.str.10                                0x08008c16   Data          49  main.o(.rodata.str1.1)
    .L.str.6                                 0x08008c47   Data          38  main.o(.rodata.str1.1)
    .L.str.27                                0x08008c6d   Data          39  main.o(.rodata.str1.1)
    .L.str.11                                0x08008c94   Data          29  main.o(.rodata.str1.1)
    .L.str.25                                0x08008cb1   Data          50  main.o(.rodata.str1.1)
    .L.str.7                                 0x08008ce3   Data          36  main.o(.rodata.str1.1)
    .L.str.18                                0x08008d07   Data          40  main.o(.rodata.str1.1)
    .L.str.21                                0x08008d2f   Data          38  main.o(.rodata.str1.1)
    .L.str.14                                0x08008d55   Data          41  main.o(.rodata.str1.1)
    .L.str.24                                0x08008d7e   Data          59  main.o(.rodata.str1.1)
    .L.str.20                                0x08008db9   Data          60  main.o(.rodata.str1.1)
    .L.str.13                                0x08008df5   Data          59  main.o(.rodata.str1.1)
    .L.str.4                                 0x08008e30   Data          50  main.o(.rodata.str1.1)
    .L.str.8                                 0x08008e62   Data          31  main.o(.rodata.str1.1)
    .L.str.19                                0x08008e81   Data          28  main.o(.rodata.str1.1)
    .L.str.12                                0x08008e9d   Data          26  main.o(.rodata.str1.1)
    .L.str.16                                0x08008eb7   Data          30  main.o(.rodata.str1.1)
    .L.str.5                                 0x08008ed5   Data          26  main.o(.rodata.str1.1)
    .L.str.23                                0x08008eef   Data          29  main.o(.rodata.str1.1)
    .L.str.15                                0x08008f0c   Data          29  main.o(.rodata.str1.1)
    .L.str.9                                 0x08008f29   Data          10  main.o(.rodata.str1.1)
    .L.str.30                                0x08008f32   Data           1  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x08008f33   Section        0  usbh_msc.o(.rodata.str1.1)
    .L__const.MSC_Application.wtext          0x08008f38   Data          56  main.o(.rodata.str1.4)
    [Anonymous Symbol]                       0x08008f38   Section        0  main.o(.rodata.str1.4)
    .data                                    0x20000000   Section        4  mvars.o(.data)
    .data                                    0x20000004   Section        4  mvars.o(.data)
    .L_MergedGlobals                         0x20000008   Data           8  stm32f4xx_hal.o(.data..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000008   Section        0  stm32f4xx_hal.o(.data..L_MergedGlobals)
    .L_MergedGlobals                         0x20000038   Data           5  main.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000038   Section        0  main.o(.bss..L_MergedGlobals)
    .L_MergedGlobals                         0x20000040   Data          44  ff.o(.bss..L_MergedGlobals)
    Fsid                                     0x20000040   Data           2  ff.o(.bss..L_MergedGlobals)
    [Anonymous Symbol]                       0x20000040   Section        0  ff.o(.bss..L_MergedGlobals)
    FatFs                                    0x20000044   Data           8  ff.o(.bss..L_MergedGlobals)
    Files                                    0x2000004c   Data          32  ff.o(.bss..L_MergedGlobals)
    scratch                                  0x20000ddc   Data         512  usbh_diskio_dma.o(.bss.scratch)
    [Anonymous Symbol]                       0x20000ddc   Section        0  usbh_diskio_dma.o(.bss.scratch)
    HEAP                                     0x20000fe0   Section     1024  startup_stm32f429xx.o(HEAP)
    STACK                                    0x200013e0   Section     1344  startup_stm32f429xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001bd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001bd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001c1   Thumb Code     8  startup_stm32f429xx.o(.text)
    ADC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DCMI_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2D_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream2_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI0_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI1_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI2_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI3_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    FLASH_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    FMC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    FPU_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    LTDC_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    PVD_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    RCC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SAI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SDIO_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI2_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    SPI6_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM2_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM7_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART5_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART7_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    UART8_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART1_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART2_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART3_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    USART6_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    WWDG_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429xx.o(.text)
    __aeabi_uldivmod                         0x080001e5   Thumb Code    98  uldiv.o(.text)
    __aeabi_memcpy                           0x08000247   Thumb Code    36  memcpya.o(.text)
    __aeabi_memcpy4                          0x08000247   Thumb Code     0  memcpya.o(.text)
    __aeabi_memcpy8                          0x08000247   Thumb Code     0  memcpya.o(.text)
    __aeabi_memset                           0x0800026b   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x0800026b   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x0800026b   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000279   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000279   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000279   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x0800027d   Thumb Code    18  memseta.o(.text)
    strlen                                   0x0800028f   Thumb Code    14  strlen.o(.text)
    calloc                                   0x0800029d   Thumb Code    28  calloc.o(.text)
    __aeabi_uidiv                            0x080002b9   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x080002b9   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x080002e5   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080002e5   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000303   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000303   Thumb Code     0  llushr.o(.text)
    __I$use$fp                               0x08000323   Thumb Code     0  iusefp.o(.text)
    __aeabi_dadd                             0x08000323   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000465   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x0800046b   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000471   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000555   Thumb Code   222  ddiv.o(.text)
    __aeabi_d2ulz                            0x08000633   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08000665   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08000695   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x08000695   Thumb Code     0  init.o(.text)
    __aeabi_lasr                             0x080006c5   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080006c5   Thumb Code     0  llsshr.o(.text)
    _double_round                            0x080006e9   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x08000707   Thumb Code   156  depilogue.o(.text)
    BusFault_Handler                         0x080007a5   Thumb Code     2  stm32f4xx_it.o(.text.BusFault_Handler)
    Custom_LED_Init                          0x080007a9   Thumb Code   184  main.o(.text.Custom_LED_Init)
    DebugMon_Handler                         0x08000861   Thumb Code     2  stm32f4xx_it.o(.text.DebugMon_Handler)
    Debug_Printf                             0x08000865   Thumb Code    66  main.o(.text.Debug_Printf)
    Debug_UART_Init                          0x080008a9   Thumb Code   206  main.o(.text.Debug_UART_Init)
    FATFS_LinkDriver                         0x080009a5   Thumb Code    76  ff_gen_drv.o(.text.FATFS_LinkDriver)
    FATFS_UnLinkDriver                       0x080009f1   Thumb Code    48  ff_gen_drv.o(.text.FATFS_UnLinkDriver)
    HAL_Delay                                0x08000a21   Thumb Code    40  stm32f4xx_hal.o(.text.HAL_Delay)
    HAL_GPIO_Init                            0x08000a49   Thumb Code   414  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_Init)
    HAL_GPIO_WritePin                        0x08000be9   Thumb Code    10  stm32f4xx_hal_gpio.o(.text.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08000bf5   Thumb Code    12  stm32f4xx_hal.o(.text.HAL_GetTick)
    HAL_HCD_Connect_Callback                 0x08000c01   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_Connect_Callback)
    HAL_HCD_Disconnect_Callback              0x08000c09   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_Disconnect_Callback)
    HAL_HCD_GetCurrentFrame                  0x08000c11   Thumb Code     6  stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentFrame)
    HAL_HCD_GetCurrentSpeed                  0x08000c19   Thumb Code     6  stm32f4xx_hal_hcd.o(.text.HAL_HCD_GetCurrentSpeed)
    HAL_HCD_HC_GetURBState                   0x08000c21   Thumb Code    14  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetURBState)
    HAL_HCD_HC_GetXferCount                  0x08000c31   Thumb Code    12  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_GetXferCount)
    HAL_HCD_HC_Halt                          0x08000c3d   Thumb Code    38  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Halt)
    HAL_HCD_HC_Init                          0x08000c65   Thumb Code   166  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_Init)
    HAL_HCD_HC_NotifyURBChange_Callback      0x08000d0d   Thumb Code     2  usbh_conf.o(.text.HAL_HCD_HC_NotifyURBChange_Callback)
    HAL_HCD_HC_SubmitRequest                 0x08000d11   Thumb Code   162  stm32f4xx_hal_hcd.o(.text.HAL_HCD_HC_SubmitRequest)
    HAL_HCD_IRQHandler                       0x08000db5   Thumb Code  2384  stm32f4xx_hal_hcd.o(.text.HAL_HCD_IRQHandler)
    HAL_HCD_Init                             0x08001705   Thumb Code   138  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Init)
    HAL_HCD_MspInit                          0x08001791   Thumb Code   146  usbh_conf.o(.text.HAL_HCD_MspInit)
    HAL_HCD_PortDisabled_Callback            0x08001825   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_PortDisabled_Callback)
    HAL_HCD_PortEnabled_Callback             0x0800182d   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_PortEnabled_Callback)
    HAL_HCD_ResetPort                        0x08001835   Thumb Code     6  stm32f4xx_hal_hcd.o(.text.HAL_HCD_ResetPort)
    HAL_HCD_SOF_Callback                     0x0800183d   Thumb Code     8  usbh_conf.o(.text.HAL_HCD_SOF_Callback)
    HAL_HCD_Start                            0x08001845   Thumb Code    46  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Start)
    HAL_HCD_Stop                             0x08001875   Thumb Code    38  stm32f4xx_hal_hcd.o(.text.HAL_HCD_Stop)
    HAL_IncTick                              0x0800189d   Thumb Code    26  stm32f4xx_hal.o(.text.HAL_IncTick)
    HAL_Init                                 0x080018b9   Thumb Code    54  stm32f4xx_hal.o(.text.HAL_Init)
    HAL_InitTick                             0x080018f1   Thumb Code    72  stm32f4xx_hal.o(.text.HAL_InitTick)
    HAL_MspInit                              0x08001939   Thumb Code     2  stm32f4xx_hal.o(.text.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x0800193d   Thumb Code    34  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08001961   Thumb Code    86  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x080019b9   Thumb Code    32  stm32f4xx_hal_cortex.o(.text.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x080019d9   Thumb Code   356  stm32f4xx_hal_rcc.o(.text.HAL_RCC_ClockConfig)
    HAL_RCC_GetPCLK1Freq                     0x08001b3d   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08001b65   Thumb Code    38  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08001b8d   Thumb Code   112  stm32f4xx_hal_rcc.o(.text.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x08001bfd   Thumb Code   940  stm32f4xx_hal_rcc.o(.text.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x08001fa9   Thumb Code    44  stm32f4xx_hal_cortex.o(.text.HAL_SYSTICK_Config)
    HAL_UART_Init                            0x08001fd5   Thumb Code    96  stm32f4xx_hal_uart.o(.text.HAL_UART_Init)
    HAL_UART_MspInit                         0x08002035   Thumb Code     2  stm32f4xx_hal_uart.o(.text.HAL_UART_MspInit)
    HAL_UART_Transmit                        0x08002039   Thumb Code   402  stm32f4xx_hal_uart.o(.text.HAL_UART_Transmit)
    HardFault_Handler                        0x080021cd   Thumb Code     2  stm32f4xx_it.o(.text.HardFault_Handler)
    MemManage_Handler                        0x080021d1   Thumb Code     2  stm32f4xx_it.o(.text.MemManage_Handler)
    NMI_Handler                              0x080021d5   Thumb Code     2  stm32f4xx_it.o(.text.NMI_Handler)
    OTG_HS_IRQHandler                        0x080021d9   Thumb Code    12  stm32f4xx_it.o(.text.OTG_HS_IRQHandler)
    PendSV_Handler                           0x080021e5   Thumb Code     2  stm32f4xx_it.o(.text.PendSV_Handler)
    SVC_Handler                              0x080021e9   Thumb Code     2  stm32f4xx_it.o(.text.SVC_Handler)
    SysTick_Handler                          0x080021ed   Thumb Code     4  stm32f4xx_it.o(.text.SysTick_Handler)
    SystemInit                               0x080021f1   Thumb Code    86  system_stm32f4xx.o(.text.SystemInit)
    USBH_AllocPipe                           0x08002331   Thumb Code   210  usbh_pipes.o(.text.USBH_AllocPipe)
    USBH_BulkReceiveData                     0x08002405   Thumb Code    38  usbh_ioreq.o(.text.USBH_BulkReceiveData)
    USBH_BulkSendData                        0x0800242d   Thumb Code    50  usbh_ioreq.o(.text.USBH_BulkSendData)
    USBH_ClosePipe                           0x08002461   Thumb Code    10  usbh_pipes.o(.text.USBH_ClosePipe)
    USBH_ClrFeature                          0x0800246d   Thumb Code    26  usbh_ctlreq.o(.text.USBH_ClrFeature)
    USBH_CtlReceiveData                      0x08002489   Thumb Code    38  usbh_ioreq.o(.text.USBH_CtlReceiveData)
    USBH_CtlReq                              0x080024b1   Thumb Code   482  usbh_ctlreq.o(.text.USBH_CtlReq)
    USBH_CtlSendData                         0x08002695   Thumb Code    50  usbh_ioreq.o(.text.USBH_CtlSendData)
    USBH_CtlSendSetup                        0x080026c9   Thumb Code    36  usbh_ioreq.o(.text.USBH_CtlSendSetup)
    USBH_Delay                               0x080026ed   Thumb Code     4  usbh_conf.o(.text.USBH_Delay)
    USBH_FindInterface                       0x080026f1   Thumb Code   250  usbh_core.o(.text.USBH_FindInterface)
    USBH_FreePipe                            0x080027ed   Thumb Code    24  usbh_pipes.o(.text.USBH_FreePipe)
    USBH_Get_CfgDesc                         0x08002805   Thumb Code   656  usbh_ctlreq.o(.text.USBH_Get_CfgDesc)
    USBH_Get_DevDesc                         0x08002a95   Thumb Code   186  usbh_ctlreq.o(.text.USBH_Get_DevDesc)
    USBH_Get_StringDesc                      0x08002b51   Thumb Code   188  usbh_ctlreq.o(.text.USBH_Get_StringDesc)
    USBH_Init                                0x08002c0d   Thumb Code   118  usbh_core.o(.text.USBH_Init)
    USBH_LL_ClosePipe                        0x08002c85   Thumb Code    14  usbh_conf.o(.text.USBH_LL_ClosePipe)
    USBH_LL_Connect                          0x08002c95   Thumb Code    22  usbh_core.o(.text.USBH_LL_Connect)
    USBH_LL_Disconnect                       0x08002cad   Thumb Code    46  usbh_core.o(.text.USBH_LL_Disconnect)
    USBH_LL_DriverVBUS                       0x08002cdd   Thumb Code    36  usbh_conf.o(.text.USBH_LL_DriverVBUS)
    USBH_LL_GetLastXferSize                  0x08002d01   Thumb Code     8  usbh_conf.o(.text.USBH_LL_GetLastXferSize)
    USBH_LL_GetSpeed                         0x08002d09   Thumb Code    20  usbh_conf.o(.text.USBH_LL_GetSpeed)
    USBH_LL_GetToggle                        0x08002d1d   Thumb Code    32  usbh_conf.o(.text.USBH_LL_GetToggle)
    USBH_LL_GetURBState                      0x08002d3d   Thumb Code     8  usbh_conf.o(.text.USBH_LL_GetURBState)
    USBH_LL_IncTimer                         0x08002d45   Thumb Code    30  usbh_core.o(.text.USBH_LL_IncTimer)
    USBH_LL_Init                             0x08002d65   Thumb Code    70  usbh_conf.o(.text.USBH_LL_Init)
    USBH_LL_OpenPipe                         0x08002dad   Thumb Code    30  usbh_conf.o(.text.USBH_LL_OpenPipe)
    USBH_LL_PortDisabled                     0x08002dcd   Thumb Code    14  usbh_core.o(.text.USBH_LL_PortDisabled)
    USBH_LL_PortEnabled                      0x08002ddd   Thumb Code     8  usbh_core.o(.text.USBH_LL_PortEnabled)
    USBH_LL_ResetPort                        0x08002de5   Thumb Code    14  usbh_conf.o(.text.USBH_LL_ResetPort)
    USBH_LL_SetTimer                         0x08002df5   Thumb Code     6  usbh_core.o(.text.USBH_LL_SetTimer)
    USBH_LL_SetToggle                        0x08002dfd   Thumb Code    36  usbh_conf.o(.text.USBH_LL_SetToggle)
    USBH_LL_Start                            0x08002e21   Thumb Code    14  usbh_conf.o(.text.USBH_LL_Start)
    USBH_LL_Stop                             0x08002e31   Thumb Code    14  usbh_conf.o(.text.USBH_LL_Stop)
    USBH_LL_SubmitURB                        0x08002e41   Thumb Code    34  usbh_conf.o(.text.USBH_LL_SubmitURB)
    USBH_MSC_BOT_Init                        0x08002e65   Thumb Code    40  usbh_msc_bot.o(.text.USBH_MSC_BOT_Init)
    USBH_MSC_BOT_Process                     0x08002e8d   Thumb Code   566  usbh_msc_bot.o(.text.USBH_MSC_BOT_Process)
    USBH_MSC_BOT_REQ_GetMaxLUN               0x080030c5   Thumb Code    22  usbh_msc_bot.o(.text.USBH_MSC_BOT_REQ_GetMaxLUN)
    USBH_MSC_GetLUNInfo                      0x08003155   Thumb Code    88  usbh_msc.o(.text.USBH_MSC_GetLUNInfo)
    USBH_MSC_Read                            0x080035a1   Thumb Code   114  usbh_msc.o(.text.USBH_MSC_Read)
    USBH_MSC_SCSI_Inquiry                    0x08003615   Thumb Code   176  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Inquiry)
    USBH_MSC_SCSI_Read                       0x080036c5   Thumb Code   140  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Read)
    USBH_MSC_SCSI_ReadCapacity               0x08003751   Thumb Code   112  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_ReadCapacity)
    USBH_MSC_SCSI_RequestSense               0x080037c1   Thumb Code   140  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_RequestSense)
    USBH_MSC_SCSI_TestUnitReady              0x0800384d   Thumb Code    72  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_TestUnitReady)
    USBH_MSC_SCSI_Write                      0x08003895   Thumb Code   140  usbh_msc_scsi.o(.text.USBH_MSC_SCSI_Write)
    USBH_MSC_UnitIsReady                     0x08003925   Thumb Code    38  usbh_msc.o(.text.USBH_MSC_UnitIsReady)
    USBH_MSC_Write                           0x0800394d   Thumb Code   114  usbh_msc.o(.text.USBH_MSC_Write)
    USBH_OpenPipe                            0x080039c1   Thumb Code    26  usbh_pipes.o(.text.USBH_OpenPipe)
    USBH_Process                             0x080039dd   Thumb Code  1196  usbh_core.o(.text.USBH_Process)
    USBH_RegisterClass                       0x08003e89   Thumb Code    26  usbh_core.o(.text.USBH_RegisterClass)
    USBH_SelectInterface                     0x08003ea5   Thumb Code    18  usbh_core.o(.text.USBH_SelectInterface)
    USBH_SetAddress                          0x08003eb9   Thumb Code    26  usbh_ctlreq.o(.text.USBH_SetAddress)
    USBH_SetCfg                              0x08003ed5   Thumb Code    26  usbh_ctlreq.o(.text.USBH_SetCfg)
    USBH_SetFeature                          0x08003ef1   Thumb Code    26  usbh_ctlreq.o(.text.USBH_SetFeature)
    USBH_Start                               0x08003f0d   Thumb Code    20  usbh_core.o(.text.USBH_Start)
    USBH_initialize                          0x08004009   Thumb Code     4  usbh_diskio_dma.o(.text.USBH_initialize)
    USBH_ioctl                               0x0800400d   Thumb Code   130  usbh_diskio_dma.o(.text.USBH_ioctl)
    USBH_read                                0x08004091   Thumb Code   188  usbh_diskio_dma.o(.text.USBH_read)
    USBH_status                              0x0800414d   Thumb Code    24  usbh_diskio_dma.o(.text.USBH_status)
    USBH_write                               0x08004165   Thumb Code   204  usbh_diskio_dma.o(.text.USBH_write)
    USB_CoreInit                             0x08004231   Thumb Code   630  stm32f4xx_ll_usb.o(.text.USB_CoreInit)
    USB_DisableGlobalInt                     0x080044a9   Thumb Code    14  stm32f4xx_ll_usb.o(.text.USB_DisableGlobalInt)
    USB_DriveVbus                            0x080044b9   Thumb Code    76  stm32f4xx_ll_usb.o(.text.USB_DriveVbus)
    USB_EnableGlobalInt                      0x08004505   Thumb Code    14  stm32f4xx_ll_usb.o(.text.USB_EnableGlobalInt)
    USB_FlushRxFifo                          0x08004515   Thumb Code   190  stm32f4xx_ll_usb.o(.text.USB_FlushRxFifo)
    USB_FlushTxFifo                          0x080045d5   Thumb Code   190  stm32f4xx_ll_usb.o(.text.USB_FlushTxFifo)
    USB_GetCurrentFrame                      0x08004695   Thumb Code     8  stm32f4xx_ll_usb.o(.text.USB_GetCurrentFrame)
    USB_GetHostSpeed                         0x0800469d   Thumb Code    22  stm32f4xx_ll_usb.o(.text.USB_GetHostSpeed)
    USB_GetMode                              0x080046b5   Thumb Code     8  stm32f4xx_ll_usb.o(.text.USB_GetMode)
    USB_HC_Halt                              0x080046bd   Thumb Code   386  stm32f4xx_ll_usb.o(.text.USB_HC_Halt)
    USB_HC_Init                              0x08004841   Thumb Code   294  stm32f4xx_ll_usb.o(.text.USB_HC_Init)
    USB_HC_ReadInterrupt                     0x08004969   Thumb Code     8  stm32f4xx_ll_usb.o(.text.USB_HC_ReadInterrupt)
    USB_HC_StartXfer                         0x08004971   Thumb Code   744  stm32f4xx_ll_usb.o(.text.USB_HC_StartXfer)
    USB_HostInit                             0x08004c69   Thumb Code   736  stm32f4xx_ll_usb.o(.text.USB_HostInit)
    USB_InitFSLSPClkSel                      0x08004f49   Thumb Code    64  stm32f4xx_ll_usb.o(.text.USB_InitFSLSPClkSel)
    USB_ReadChInterrupts                     0x08004f89   Thumb Code    16  stm32f4xx_ll_usb.o(.text.USB_ReadChInterrupts)
    USB_ReadInterrupts                       0x08004f99   Thumb Code     8  stm32f4xx_ll_usb.o(.text.USB_ReadInterrupts)
    USB_ReadPacket                           0x08004fa1   Thumb Code   170  stm32f4xx_ll_usb.o(.text.USB_ReadPacket)
    USB_ResetPort                            0x0800504d   Thumb Code    62  stm32f4xx_ll_usb.o(.text.USB_ResetPort)
    USB_SetCurrentMode                       0x0800508d   Thumb Code   872  stm32f4xx_ll_usb.o(.text.USB_SetCurrentMode)
    USB_StopHost                             0x080053f5   Thumb Code  2360  stm32f4xx_ll_usb.o(.text.USB_StopHost)
    UsageFault_Handler                       0x08005d2d   Thumb Code     2  stm32f4xx_it.o(.text.UsageFault_Handler)
    disk_initialize                          0x080060d1   Thumb Code    50  diskio.o(.text.disk_initialize)
    disk_ioctl                               0x08006105   Thumb Code    22  diskio.o(.text.disk_ioctl)
    disk_read                                0x0800611d   Thumb Code    32  diskio.o(.text.disk_read)
    disk_status                              0x0800613d   Thumb Code    22  diskio.o(.text.disk_status)
    disk_write                               0x08006155   Thumb Code    32  diskio.o(.text.disk_write)
    f_close                                  0x08006175   Thumb Code   108  ff.o(.text.f_close)
    f_mount                                  0x080061e1   Thumb Code   280  ff.o(.text.f_mount)
    f_open                                   0x080062f9   Thumb Code   928  ff.o(.text.f_open)
    f_read                                   0x08006699   Thumb Code   776  ff.o(.text.f_read)
    f_sync                                   0x080069a1   Thumb Code   192  ff.o(.text.f_sync)
    f_write                                  0x08006a61   Thumb Code   838  ff.o(.text.f_write)
    get_fattime                              0x08007a19   Thumb Code     4  diskio.o(.text.get_fattime)
    main                                     0x08007a1d   Thumb Code   996  main.o(.text.main)
    __0vsnprintf                             0x0800814d   Thumb Code    46  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x0800814d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x0800814d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x0800814d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x0800814d   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __scatterload_copy                       0x08008181   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800818f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08008191   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    free                                     0x08008a69   Thumb Code    76  malloc.o(i.free)
    malloc                                   0x08008ab9   Thumb Code    92  malloc.o(i.malloc)
    AHBPrescTable                            0x08008b24   Data          16  system_stm32f4xx.o(.rodata.AHBPrescTable)
    APBPrescTable                            0x08008b34   Data           8  system_stm32f4xx.o(.rodata.APBPrescTable)
    USBH_Driver                              0x08008bbc   Data          20  usbh_diskio_dma.o(.rodata.USBH_Driver)
    Region$$Table$$Base                      0x08008f70   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x08008f90   Number         0  anon$$obj.o(Region$$Table)
    __microlib_freelist                      0x20000000   Data           4  mvars.o(.data)
    __microlib_freelist_initialised          0x20000004   Data           4  mvars.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f4xx_hal.o(.data..L_MergedGlobals)
    uwTickPrio                               0x2000000c   Data           4  stm32f4xx_hal.o(.data..L_MergedGlobals)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data.SystemCoreClock)
    USBH_msc                                 0x20000014   Data          32  usbh_msc.o(.data.USBH_msc)
    Appli_state                              0x20000038   Data           1  main.o(.bss..L_MergedGlobals)
    USBDISKPath                              0x20000039   Data           4  main.o(.bss..L_MergedGlobals)
    MyFile                                   0x2000006c   Data         560  main.o(.bss.MyFile)
    USBDISKFatFs                             0x2000029c   Data         560  main.o(.bss.USBDISKFatFs)
    disk                                     0x200004cc   Data          16  ff_gen_drv.o(.bss.disk)
    hUSBHost                                 0x200004dc   Data        1240  main.o(.bss.hUSBHost)
    hhcd                                     0x200009b4   Data         992  usbh_conf.o(.bss.hhcd)
    huart1                                   0x20000d94   Data          72  main.o(.bss.huart1)
    uwTick                                   0x20000fdc   Data           4  stm32f4xx_hal.o(.bss.uwTick)
    __heap_base                              0x20000fe0   Data           0  startup_stm32f429xx.o(HEAP)
    __heap_limit                             0x200013e0   Data           0  startup_stm32f429xx.o(HEAP)
    __initial_sp                             0x20001920   Data           0  startup_stm32f429xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00008fc8, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x00008f90, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO          113    RESET               startup_stm32f429xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         1428  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         1499    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         1502    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         1504    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         1506    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         1507    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         1509    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         1511    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         1500    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c0   0x080001c0   0x00000024   Code   RO          114    .text               startup_stm32f429xx.o
    0x080001e4   0x080001e4   0x00000062   Code   RO         1431    .text               mc_w.l(uldiv.o)
    0x08000246   0x08000246   0x00000024   Code   RO         1433    .text               mc_w.l(memcpya.o)
    0x0800026a   0x0800026a   0x00000024   Code   RO         1435    .text               mc_w.l(memseta.o)
    0x0800028e   0x0800028e   0x0000000e   Code   RO         1437    .text               mc_w.l(strlen.o)
    0x0800029c   0x0800029c   0x0000001c   Code   RO         1497    .text               mc_w.l(calloc.o)
    0x080002b8   0x080002b8   0x0000002c   Code   RO         1527    .text               mc_w.l(uidiv.o)
    0x080002e4   0x080002e4   0x0000001e   Code   RO         1529    .text               mc_w.l(llshl.o)
    0x08000302   0x08000302   0x00000020   Code   RO         1531    .text               mc_w.l(llushr.o)
    0x08000322   0x08000322   0x00000000   Code   RO         1535    .text               mc_w.l(iusefp.o)
    0x08000322   0x08000322   0x0000014e   Code   RO         1536    .text               mf_w.l(dadd.o)
    0x08000470   0x08000470   0x000000e4   Code   RO         1538    .text               mf_w.l(dmul.o)
    0x08000554   0x08000554   0x000000de   Code   RO         1540    .text               mf_w.l(ddiv.o)
    0x08000632   0x08000632   0x00000030   Code   RO         1542    .text               mf_w.l(dfixul.o)
    0x08000662   0x08000662   0x00000002   PAD
    0x08000664   0x08000664   0x00000030   Code   RO         1544    .text               mf_w.l(cdrcmple.o)
    0x08000694   0x08000694   0x00000030   Code   RO         1546    .text               mc_w.l(init.o)
    0x080006c4   0x080006c4   0x00000024   Code   RO         1549    .text               mc_w.l(llsshr.o)
    0x080006e8   0x080006e8   0x000000ba   Code   RO         1552    .text               mf_w.l(depilogue.o)
    0x080007a2   0x080007a2   0x00000002   PAD
    0x080007a4   0x080007a4   0x00000002   Code   RO            8    .text.BusFault_Handler  stm32f4xx_it.o
    0x080007a6   0x080007a6   0x00000002   PAD
    0x080007a8   0x080007a8   0x000000b8   Code   RO           86    .text.Custom_LED_Init  main.o
    0x08000860   0x08000860   0x00000002   Code   RO           14    .text.DebugMon_Handler  stm32f4xx_it.o
    0x08000862   0x08000862   0x00000002   PAD
    0x08000864   0x08000864   0x00000042   Code   RO           90    .text.Debug_Printf  main.o
    0x080008a6   0x080008a6   0x00000002   PAD
    0x080008a8   0x080008a8   0x000000ce   Code   RO           88    .text.Debug_UART_Init  main.o
    0x08000976   0x08000976   0x00000002   PAD
    0x08000978   0x08000978   0x0000002a   Code   RO           94    .text.Error_Handler  main.o
    0x080009a2   0x080009a2   0x00000002   PAD
    0x080009a4   0x080009a4   0x0000004c   Code   RO          239    .text.FATFS_LinkDriver  ff_gen_drv.o
    0x080009f0   0x080009f0   0x00000030   Code   RO          243    .text.FATFS_UnLinkDriver  ff_gen_drv.o
    0x08000a20   0x08000a20   0x00000028   Code   RO          541    .text.HAL_Delay     stm32f4xx_hal.o
    0x08000a48   0x08000a48   0x0000019e   Code   RO          785    .text.HAL_GPIO_Init  stm32f4xx_hal_gpio.o
    0x08000be6   0x08000be6   0x00000002   PAD
    0x08000be8   0x08000be8   0x0000000a   Code   RO          791    .text.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08000bf2   0x08000bf2   0x00000002   PAD
    0x08000bf4   0x08000bf4   0x0000000c   Code   RO          533    .text.HAL_GetTick   stm32f4xx_hal.o
    0x08000c00   0x08000c00   0x00000008   Code   RO           35    .text.HAL_HCD_Connect_Callback  usbh_conf.o
    0x08000c08   0x08000c08   0x00000008   Code   RO           37    .text.HAL_HCD_Disconnect_Callback  usbh_conf.o
    0x08000c10   0x08000c10   0x00000006   Code   RO         1236    .text.HAL_HCD_GetCurrentFrame  stm32f4xx_hal_hcd.o
    0x08000c16   0x08000c16   0x00000002   PAD
    0x08000c18   0x08000c18   0x00000006   Code   RO         1238    .text.HAL_HCD_GetCurrentSpeed  stm32f4xx_hal_hcd.o
    0x08000c1e   0x08000c1e   0x00000002   PAD
    0x08000c20   0x08000c20   0x0000000e   Code   RO         1230    .text.HAL_HCD_HC_GetURBState  stm32f4xx_hal_hcd.o
    0x08000c2e   0x08000c2e   0x00000002   PAD
    0x08000c30   0x08000c30   0x0000000c   Code   RO         1232    .text.HAL_HCD_HC_GetXferCount  stm32f4xx_hal_hcd.o
    0x08000c3c   0x08000c3c   0x00000026   Code   RO         1198    .text.HAL_HCD_HC_Halt  stm32f4xx_hal_hcd.o
    0x08000c62   0x08000c62   0x00000002   PAD
    0x08000c64   0x08000c64   0x000000a6   Code   RO         1194    .text.HAL_HCD_HC_Init  stm32f4xx_hal_hcd.o
    0x08000d0a   0x08000d0a   0x00000002   PAD
    0x08000d0c   0x08000d0c   0x00000002   Code   RO           43    .text.HAL_HCD_HC_NotifyURBChange_Callback  usbh_conf.o
    0x08000d0e   0x08000d0e   0x00000002   PAD
    0x08000d10   0x08000d10   0x000000a2   Code   RO         1204    .text.HAL_HCD_HC_SubmitRequest  stm32f4xx_hal_hcd.o
    0x08000db2   0x08000db2   0x00000002   PAD
    0x08000db4   0x08000db4   0x00000950   Code   RO         1206    .text.HAL_HCD_IRQHandler  stm32f4xx_hal_hcd.o
    0x08001704   0x08001704   0x0000008a   Code   RO         1190    .text.HAL_HCD_Init  stm32f4xx_hal_hcd.o
    0x0800178e   0x0800178e   0x00000002   PAD
    0x08001790   0x08001790   0x00000092   Code   RO           29    .text.HAL_HCD_MspInit  usbh_conf.o
    0x08001822   0x08001822   0x00000002   PAD
    0x08001824   0x08001824   0x00000008   Code   RO           41    .text.HAL_HCD_PortDisabled_Callback  usbh_conf.o
    0x0800182c   0x0800182c   0x00000008   Code   RO           39    .text.HAL_HCD_PortEnabled_Callback  usbh_conf.o
    0x08001834   0x08001834   0x00000006   Code   RO         1226    .text.HAL_HCD_ResetPort  stm32f4xx_hal_hcd.o
    0x0800183a   0x0800183a   0x00000002   PAD
    0x0800183c   0x0800183c   0x00000008   Code   RO           33    .text.HAL_HCD_SOF_Callback  usbh_conf.o
    0x08001844   0x08001844   0x0000002e   Code   RO         1222    .text.HAL_HCD_Start  stm32f4xx_hal_hcd.o
    0x08001872   0x08001872   0x00000002   PAD
    0x08001874   0x08001874   0x00000026   Code   RO         1224    .text.HAL_HCD_Stop  stm32f4xx_hal_hcd.o
    0x0800189a   0x0800189a   0x00000002   PAD
    0x0800189c   0x0800189c   0x0000001a   Code   RO          531    .text.HAL_IncTick   stm32f4xx_hal.o
    0x080018b6   0x080018b6   0x00000002   PAD
    0x080018b8   0x080018b8   0x00000036   Code   RO          521    .text.HAL_Init      stm32f4xx_hal.o
    0x080018ee   0x080018ee   0x00000002   PAD
    0x080018f0   0x080018f0   0x00000048   Code   RO          523    .text.HAL_InitTick  stm32f4xx_hal.o
    0x08001938   0x08001938   0x00000002   Code   RO          525    .text.HAL_MspInit   stm32f4xx_hal.o
    0x0800193a   0x0800193a   0x00000002   PAD
    0x0800193c   0x0800193c   0x00000022   Code   RO         1254    .text.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x0800195e   0x0800195e   0x00000002   PAD
    0x08001960   0x08001960   0x00000056   Code   RO         1252    .text.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x080019b6   0x080019b6   0x00000002   PAD
    0x080019b8   0x080019b8   0x00000020   Code   RO         1250    .text.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x080019d8   0x080019d8   0x00000164   Code   RO          593    .text.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08001b3c   0x08001b3c   0x00000026   Code   RO          605    .text.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08001b62   0x08001b62   0x00000002   PAD
    0x08001b64   0x08001b64   0x00000026   Code   RO          607    .text.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08001b8a   0x08001b8a   0x00000002   PAD
    0x08001b8c   0x08001b8c   0x00000070   Code   RO          595    .text.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x08001bfc   0x08001bfc   0x000003ac   Code   RO          591    .text.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x08001fa8   0x08001fa8   0x0000002c   Code   RO         1262    .text.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001fd4   0x08001fd4   0x00000060   Code   RO          651    .text.HAL_UART_Init  stm32f4xx_hal_uart.o
    0x08002034   0x08002034   0x00000002   Code   RO          653    .text.HAL_UART_MspInit  stm32f4xx_hal_uart.o
    0x08002036   0x08002036   0x00000002   PAD
    0x08002038   0x08002038   0x00000192   Code   RO          667    .text.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x080021ca   0x080021ca   0x00000002   PAD
    0x080021cc   0x080021cc   0x00000002   Code   RO            4    .text.HardFault_Handler  stm32f4xx_it.o
    0x080021ce   0x080021ce   0x00000002   PAD
    0x080021d0   0x080021d0   0x00000002   Code   RO            6    .text.MemManage_Handler  stm32f4xx_it.o
    0x080021d2   0x080021d2   0x00000002   PAD
    0x080021d4   0x080021d4   0x00000002   Code   RO            2    .text.NMI_Handler   stm32f4xx_it.o
    0x080021d6   0x080021d6   0x00000002   PAD
    0x080021d8   0x080021d8   0x0000000c   Code   RO           20    .text.OTG_HS_IRQHandler  stm32f4xx_it.o
    0x080021e4   0x080021e4   0x00000002   Code   RO           16    .text.PendSV_Handler  stm32f4xx_it.o
    0x080021e6   0x080021e6   0x00000002   PAD
    0x080021e8   0x080021e8   0x00000002   Code   RO           12    .text.SVC_Handler   stm32f4xx_it.o
    0x080021ea   0x080021ea   0x00000002   PAD
    0x080021ec   0x080021ec   0x00000004   Code   RO           18    .text.SysTick_Handler  stm32f4xx_it.o
    0x080021f0   0x080021f0   0x00000056   Code   RO         1414    .text.SystemInit    system_stm32f4xx.o
    0x08002246   0x08002246   0x00000002   PAD
    0x08002248   0x08002248   0x000000e6   Code   RO          655    .text.UART_SetConfig  stm32f4xx_hal_uart.o
    0x0800232e   0x0800232e   0x00000002   PAD
    0x08002330   0x08002330   0x000000d2   Code   RO          125    .text.USBH_AllocPipe  usbh_pipes.o
    0x08002402   0x08002402   0x00000002   PAD
    0x08002404   0x08002404   0x00000026   Code   RO          145    .text.USBH_BulkReceiveData  usbh_ioreq.o
    0x0800242a   0x0800242a   0x00000002   PAD
    0x0800242c   0x0800242c   0x00000032   Code   RO          143    .text.USBH_BulkSendData  usbh_ioreq.o
    0x0800245e   0x0800245e   0x00000002   PAD
    0x08002460   0x08002460   0x0000000a   Code   RO          123    .text.USBH_ClosePipe  usbh_pipes.o
    0x0800246a   0x0800246a   0x00000002   PAD
    0x0800246c   0x0800246c   0x0000001a   Code   RO          225    .text.USBH_ClrFeature  usbh_ctlreq.o
    0x08002486   0x08002486   0x00000002   PAD
    0x08002488   0x08002488   0x00000026   Code   RO          141    .text.USBH_CtlReceiveData  usbh_ioreq.o
    0x080024ae   0x080024ae   0x00000002   PAD
    0x080024b0   0x080024b0   0x000001e2   Code   RO          215    .text.USBH_CtlReq   usbh_ctlreq.o
    0x08002692   0x08002692   0x00000002   PAD
    0x08002694   0x08002694   0x00000032   Code   RO          139    .text.USBH_CtlSendData  usbh_ioreq.o
    0x080026c6   0x080026c6   0x00000002   PAD
    0x080026c8   0x080026c8   0x00000024   Code   RO          137    .text.USBH_CtlSendSetup  usbh_ioreq.o
    0x080026ec   0x080026ec   0x00000004   Code   RO           73    .text.USBH_Delay    usbh_conf.o
    0x080026f0   0x080026f0   0x000000fa   Code   RO          173    .text.USBH_FindInterface  usbh_core.o
    0x080027ea   0x080027ea   0x00000002   PAD
    0x080027ec   0x080027ec   0x00000018   Code   RO          127    .text.USBH_FreePipe  usbh_pipes.o
    0x08002804   0x08002804   0x00000290   Code   RO          211    .text.USBH_Get_CfgDesc  usbh_ctlreq.o
    0x08002a94   0x08002a94   0x000000ba   Code   RO          207    .text.USBH_Get_DevDesc  usbh_ctlreq.o
    0x08002b4e   0x08002b4e   0x00000002   PAD
    0x08002b50   0x08002b50   0x000000bc   Code   RO          213    .text.USBH_Get_StringDesc  usbh_ctlreq.o
    0x08002c0c   0x08002c0c   0x00000076   Code   RO          163    .text.USBH_Init     usbh_core.o
    0x08002c82   0x08002c82   0x00000002   PAD
    0x08002c84   0x08002c84   0x0000000e   Code   RO           61    .text.USBH_LL_ClosePipe  usbh_conf.o
    0x08002c92   0x08002c92   0x00000002   PAD
    0x08002c94   0x08002c94   0x00000016   Code   RO          195    .text.USBH_LL_Connect  usbh_core.o
    0x08002caa   0x08002caa   0x00000002   PAD
    0x08002cac   0x08002cac   0x0000002e   Code   RO          197    .text.USBH_LL_Disconnect  usbh_core.o
    0x08002cda   0x08002cda   0x00000002   PAD
    0x08002cdc   0x08002cdc   0x00000024   Code   RO           67    .text.USBH_LL_DriverVBUS  usbh_conf.o
    0x08002d00   0x08002d00   0x00000008   Code   RO           57    .text.USBH_LL_GetLastXferSize  usbh_conf.o
    0x08002d08   0x08002d08   0x00000014   Code   RO           53    .text.USBH_LL_GetSpeed  usbh_conf.o
    0x08002d1c   0x08002d1c   0x00000020   Code   RO           71    .text.USBH_LL_GetToggle  usbh_conf.o
    0x08002d3c   0x08002d3c   0x00000008   Code   RO           65    .text.USBH_LL_GetURBState  usbh_conf.o
    0x08002d44   0x08002d44   0x0000001e   Code   RO          189    .text.USBH_LL_IncTimer  usbh_core.o
    0x08002d62   0x08002d62   0x00000002   PAD
    0x08002d64   0x08002d64   0x00000046   Code   RO           45    .text.USBH_LL_Init  usbh_conf.o
    0x08002daa   0x08002daa   0x00000002   PAD
    0x08002dac   0x08002dac   0x0000001e   Code   RO           59    .text.USBH_LL_OpenPipe  usbh_conf.o
    0x08002dca   0x08002dca   0x00000002   PAD
    0x08002dcc   0x08002dcc   0x0000000e   Code   RO          193    .text.USBH_LL_PortDisabled  usbh_core.o
    0x08002dda   0x08002dda   0x00000002   PAD
    0x08002ddc   0x08002ddc   0x00000008   Code   RO          191    .text.USBH_LL_PortEnabled  usbh_core.o
    0x08002de4   0x08002de4   0x0000000e   Code   RO           55    .text.USBH_LL_ResetPort  usbh_conf.o
    0x08002df2   0x08002df2   0x00000002   PAD
    0x08002df4   0x08002df4   0x00000006   Code   RO          187    .text.USBH_LL_SetTimer  usbh_core.o
    0x08002dfa   0x08002dfa   0x00000002   PAD
    0x08002dfc   0x08002dfc   0x00000024   Code   RO           69    .text.USBH_LL_SetToggle  usbh_conf.o
    0x08002e20   0x08002e20   0x0000000e   Code   RO           49    .text.USBH_LL_Start  usbh_conf.o
    0x08002e2e   0x08002e2e   0x00000002   PAD
    0x08002e30   0x08002e30   0x0000000e   Code   RO           51    .text.USBH_LL_Stop  usbh_conf.o
    0x08002e3e   0x08002e3e   0x00000002   PAD
    0x08002e40   0x08002e40   0x00000022   Code   RO           63    .text.USBH_LL_SubmitURB  usbh_conf.o
    0x08002e62   0x08002e62   0x00000002   PAD
    0x08002e64   0x08002e64   0x00000028   Code   RO         1348    .text.USBH_MSC_BOT_Init  usbh_msc_bot.o
    0x08002e8c   0x08002e8c   0x00000236   Code   RO         1350    .text.USBH_MSC_BOT_Process  usbh_msc_bot.o
    0x080030c2   0x080030c2   0x00000002   PAD
    0x080030c4   0x080030c4   0x00000016   Code   RO         1346    .text.USBH_MSC_BOT_REQ_GetMaxLUN  usbh_msc_bot.o
    0x080030da   0x080030da   0x00000002   PAD
    0x080030dc   0x080030dc   0x00000078   Code   RO         1364    .text.USBH_MSC_ClassRequest  usbh_msc.o
    0x08003154   0x08003154   0x00000058   Code   RO         1376    .text.USBH_MSC_GetLUNInfo  usbh_msc.o
    0x080031ac   0x080031ac   0x0000004e   Code   RO         1362    .text.USBH_MSC_InterfaceDeInit  usbh_msc.o
    0x080031fa   0x080031fa   0x00000002   PAD
    0x080031fc   0x080031fc   0x0000010e   Code   RO         1360    .text.USBH_MSC_InterfaceInit  usbh_msc.o
    0x0800330a   0x0800330a   0x00000002   PAD
    0x0800330c   0x0800330c   0x00000202   Code   RO         1366    .text.USBH_MSC_Process  usbh_msc.o
    0x0800350e   0x0800350e   0x00000002   PAD
    0x08003510   0x08003510   0x0000008e   Code   RO         1380    .text.USBH_MSC_RdWrProcess  usbh_msc.o
    0x0800359e   0x0800359e   0x00000002   PAD
    0x080035a0   0x080035a0   0x00000072   Code   RO         1378    .text.USBH_MSC_Read  usbh_msc.o
    0x08003612   0x08003612   0x00000002   PAD
    0x08003614   0x08003614   0x000000b0   Code   RO         1398    .text.USBH_MSC_SCSI_Inquiry  usbh_msc_scsi.o
    0x080036c4   0x080036c4   0x0000008c   Code   RO         1404    .text.USBH_MSC_SCSI_Read  usbh_msc_scsi.o
    0x08003750   0x08003750   0x00000070   Code   RO         1396    .text.USBH_MSC_SCSI_ReadCapacity  usbh_msc_scsi.o
    0x080037c0   0x080037c0   0x0000008c   Code   RO         1400    .text.USBH_MSC_SCSI_RequestSense  usbh_msc_scsi.o
    0x0800384c   0x0800384c   0x00000048   Code   RO         1394    .text.USBH_MSC_SCSI_TestUnitReady  usbh_msc_scsi.o
    0x08003894   0x08003894   0x0000008c   Code   RO         1402    .text.USBH_MSC_SCSI_Write  usbh_msc_scsi.o
    0x08003920   0x08003920   0x00000004   Code   RO         1368    .text.USBH_MSC_SOFProcess  usbh_msc.o
    0x08003924   0x08003924   0x00000026   Code   RO         1374    .text.USBH_MSC_UnitIsReady  usbh_msc.o
    0x0800394a   0x0800394a   0x00000002   PAD
    0x0800394c   0x0800394c   0x00000072   Code   RO         1382    .text.USBH_MSC_Write  usbh_msc.o
    0x080039be   0x080039be   0x00000002   PAD
    0x080039c0   0x080039c0   0x0000001a   Code   RO          121    .text.USBH_OpenPipe  usbh_pipes.o
    0x080039da   0x080039da   0x00000002   PAD
    0x080039dc   0x080039dc   0x000004ac   Code   RO          185    .text.USBH_Process  usbh_core.o
    0x08003e88   0x08003e88   0x0000001a   Code   RO          167    .text.USBH_RegisterClass  usbh_core.o
    0x08003ea2   0x08003ea2   0x00000002   PAD
    0x08003ea4   0x08003ea4   0x00000012   Code   RO          169    .text.USBH_SelectInterface  usbh_core.o
    0x08003eb6   0x08003eb6   0x00000002   PAD
    0x08003eb8   0x08003eb8   0x0000001a   Code   RO          217    .text.USBH_SetAddress  usbh_ctlreq.o
    0x08003ed2   0x08003ed2   0x00000002   PAD
    0x08003ed4   0x08003ed4   0x0000001a   Code   RO          219    .text.USBH_SetCfg   usbh_ctlreq.o
    0x08003eee   0x08003eee   0x00000002   PAD
    0x08003ef0   0x08003ef0   0x0000001a   Code   RO          223    .text.USBH_SetFeature  usbh_ctlreq.o
    0x08003f0a   0x08003f0a   0x00000002   PAD
    0x08003f0c   0x08003f0c   0x00000014   Code   RO          177    .text.USBH_Start    usbh_core.o
    0x08003f20   0x08003f20   0x000000e8   Code   RO           92    .text.USBH_UserProcess  main.o
    0x08004008   0x08004008   0x00000004   Code   RO          346    .text.USBH_initialize  usbh_diskio_dma.o
    0x0800400c   0x0800400c   0x00000082   Code   RO          354    .text.USBH_ioctl    usbh_diskio_dma.o
    0x0800408e   0x0800408e   0x00000002   PAD
    0x08004090   0x08004090   0x000000bc   Code   RO          350    .text.USBH_read     usbh_diskio_dma.o
    0x0800414c   0x0800414c   0x00000018   Code   RO          348    .text.USBH_status   usbh_diskio_dma.o
    0x08004164   0x08004164   0x000000cc   Code   RO          352    .text.USBH_write    usbh_diskio_dma.o
    0x08004230   0x08004230   0x00000276   Code   RO          366    .text.USB_CoreInit  stm32f4xx_ll_usb.o
    0x080044a6   0x080044a6   0x00000002   PAD
    0x080044a8   0x080044a8   0x0000000e   Code   RO          372    .text.USB_DisableGlobalInt  stm32f4xx_ll_usb.o
    0x080044b6   0x080044b6   0x00000002   PAD
    0x080044b8   0x080044b8   0x0000004c   Code   RO          440    .text.USB_DriveVbus  stm32f4xx_ll_usb.o
    0x08004504   0x08004504   0x0000000e   Code   RO          370    .text.USB_EnableGlobalInt  stm32f4xx_ll_usb.o
    0x08004512   0x08004512   0x00000002   PAD
    0x08004514   0x08004514   0x000000be   Code   RO          384    .text.USB_FlushRxFifo  stm32f4xx_ll_usb.o
    0x080045d2   0x080045d2   0x00000002   PAD
    0x080045d4   0x080045d4   0x000000be   Code   RO          382    .text.USB_FlushTxFifo  stm32f4xx_ll_usb.o
    0x08004692   0x08004692   0x00000002   PAD
    0x08004694   0x08004694   0x00000008   Code   RO          444    .text.USB_GetCurrentFrame  stm32f4xx_ll_usb.o
    0x0800469c   0x0800469c   0x00000016   Code   RO          442    .text.USB_GetHostSpeed  stm32f4xx_ll_usb.o
    0x080046b2   0x080046b2   0x00000002   PAD
    0x080046b4   0x080046b4   0x00000008   Code   RO          376    .text.USB_GetMode   stm32f4xx_ll_usb.o
    0x080046bc   0x080046bc   0x00000182   Code   RO          454    .text.USB_HC_Halt   stm32f4xx_ll_usb.o
    0x0800483e   0x0800483e   0x00000002   PAD
    0x08004840   0x08004840   0x00000126   Code   RO          446    .text.USB_HC_Init   stm32f4xx_ll_usb.o
    0x08004966   0x08004966   0x00000002   PAD
    0x08004968   0x08004968   0x00000008   Code   RO          452    .text.USB_HC_ReadInterrupt  stm32f4xx_ll_usb.o
    0x08004970   0x08004970   0x000002f8   Code   RO          448    .text.USB_HC_StartXfer  stm32f4xx_ll_usb.o
    0x08004c68   0x08004c68   0x000002e0   Code   RO          434    .text.USB_HostInit  stm32f4xx_ll_usb.o
    0x08004f48   0x08004f48   0x00000040   Code   RO          436    .text.USB_InitFSLSPClkSel  stm32f4xx_ll_usb.o
    0x08004f88   0x08004f88   0x00000010   Code   RO          418    .text.USB_ReadChInterrupts  stm32f4xx_ll_usb.o
    0x08004f98   0x08004f98   0x00000008   Code   RO          416    .text.USB_ReadInterrupts  stm32f4xx_ll_usb.o
    0x08004fa0   0x08004fa0   0x000000aa   Code   RO          402    .text.USB_ReadPacket  stm32f4xx_ll_usb.o
    0x0800504a   0x0800504a   0x00000002   PAD
    0x0800504c   0x0800504c   0x0000003e   Code   RO          438    .text.USB_ResetPort  stm32f4xx_ll_usb.o
    0x0800508a   0x0800508a   0x00000002   PAD
    0x0800508c   0x0800508c   0x00000368   Code   RO          374    .text.USB_SetCurrentMode  stm32f4xx_ll_usb.o
    0x080053f4   0x080053f4   0x00000938   Code   RO          456    .text.USB_StopHost  stm32f4xx_ll_usb.o
    0x08005d2c   0x08005d2c   0x00000002   Code   RO           10    .text.UsageFault_Handler  stm32f4xx_it.o
    0x08005d2e   0x08005d2e   0x00000002   PAD
    0x08005d30   0x08005d30   0x0000011e   Code   RO          298    .text.create_chain  ff.o
    0x08005e4e   0x08005e4e   0x00000002   PAD
    0x08005e50   0x08005e50   0x0000015e   Code   RO          316    .text.dir_next      ff.o
    0x08005fae   0x08005fae   0x00000002   PAD
    0x08005fb0   0x08005fb0   0x00000120   Code   RO          284    .text.dir_register  ff.o
    0x080060d0   0x080060d0   0x00000032   Code   RO          258    .text.disk_initialize  diskio.o
    0x08006102   0x08006102   0x00000002   PAD
    0x08006104   0x08006104   0x00000016   Code   RO          264    .text.disk_ioctl    diskio.o
    0x0800611a   0x0800611a   0x00000002   PAD
    0x0800611c   0x0800611c   0x00000020   Code   RO          260    .text.disk_read     diskio.o
    0x0800613c   0x0800613c   0x00000016   Code   RO          256    .text.disk_status   diskio.o
    0x08006152   0x08006152   0x00000002   PAD
    0x08006154   0x08006154   0x00000020   Code   RO          262    .text.disk_write    diskio.o
    0x08006174   0x08006174   0x0000006c   Code   RO          304    .text.f_close       ff.o
    0x080061e0   0x080061e0   0x00000118   Code   RO          276    .text.f_mount       ff.o
    0x080062f8   0x080062f8   0x000003a0   Code   RO          280    .text.f_open        ff.o
    0x08006698   0x08006698   0x00000308   Code   RO          294    .text.f_read        ff.o
    0x080069a0   0x080069a0   0x000000c0   Code   RO          300    .text.f_sync        ff.o
    0x08006a60   0x08006a60   0x00000346   Code   RO          296    .text.f_write       ff.o
    0x08006da6   0x08006da6   0x00000002   PAD
    0x08006da8   0x08006da8   0x0000053e   Code   RO          278    .text.find_volume   ff.o
    0x080072e6   0x080072e6   0x00000002   PAD
    0x080072e8   0x080072e8   0x00000668   Code   RO          282    .text.follow_path   ff.o
    0x08007950   0x08007950   0x000000c8   Code   RO          292    .text.get_fat       ff.o
    0x08007a18   0x08007a18   0x00000004   Code   RO          266    .text.get_fattime   diskio.o
    0x08007a1c   0x08007a1c   0x00000474   Code   RO           84    .text.main          main.o
    0x08007e90   0x08007e90   0x0000007c   Code   RO          288    .text.move_window   ff.o
    0x08007f0c   0x08007f0c   0x00000118   Code   RO          334    .text.put_fat       ff.o
    0x08008024   0x08008024   0x000000cc   Code   RO          302    .text.sync_fs       ff.o
    0x080080f0   0x080080f0   0x0000005a   Code   RO          328    .text.sync_window   ff.o
    0x0800814a   0x0800814a   0x00000002   PAD
    0x0800814c   0x0800814c   0x00000034   Code   RO         1447    i.__0vsnprintf      mc_w.l(printfa.o)
    0x08008180   0x08008180   0x0000000e   Code   RO         1556    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800818e   0x0800818e   0x00000002   Code   RO         1557    i.__scatterload_null  mc_w.l(handlers.o)
    0x08008190   0x08008190   0x0000000e   Code   RO         1558    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800819e   0x0800819e   0x00000002   PAD
    0x080081a0   0x080081a0   0x00000184   Code   RO         1449    i._fp_digits        mc_w.l(printfa.o)
    0x08008324   0x08008324   0x000006dc   Code   RO         1450    i._printf_core      mc_w.l(printfa.o)
    0x08008a00   0x08008a00   0x00000024   Code   RO         1451    i._printf_post_padding  mc_w.l(printfa.o)
    0x08008a24   0x08008a24   0x0000002e   Code   RO         1452    i._printf_pre_padding  mc_w.l(printfa.o)
    0x08008a52   0x08008a52   0x00000016   Code   RO         1453    i._snputc           mc_w.l(printfa.o)
    0x08008a68   0x08008a68   0x00000050   Code   RO         1469    i.free              mc_w.l(malloc.o)
    0x08008ab8   0x08008ab8   0x0000006c   Code   RO         1470    i.malloc            mc_w.l(malloc.o)
    0x08008b24   0x08008b24   0x00000010   Data   RO         1419    .rodata.AHBPrescTable  system_stm32f4xx.o
    0x08008b34   0x08008b34   0x00000008   Data   RO         1420    .rodata.APBPrescTable  system_stm32f4xx.o
    0x08008b3c   0x08008b3c   0x00000080   Data   RO          336    .rodata.ExCvt       ff.o
    0x08008bbc   0x08008bbc   0x00000014   Data   RO          356    .rodata.USBH_Driver  usbh_diskio_dma.o
    0x08008bd0   0x08008bd0   0x00000363   Data   RO          101    .rodata.str1.1      main.o
    0x08008f33   0x08008f33   0x00000004   Data   RO         1384    .rodata.str1.1      usbh_msc.o
    0x08008f37   0x08008f37   0x00000001   PAD
    0x08008f38   0x08008f38   0x00000038   Data   RO          100    .rodata.str1.4      main.o
    0x08008f70   0x08008f70   0x00000020   Data   RO         1555    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x08008f90, Size: 0x00001920, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x08008f90   0x00000004   Data   RW         1533    .data               mc_w.l(mvars.o)
    0x20000004   0x08008f94   0x00000004   Data   RW         1534    .data               mc_w.l(mvars.o)
    0x20000008   0x08008f98   0x00000008   Data   RW          580    .data..L_MergedGlobals  stm32f4xx_hal.o
    0x20000010   0x08008fa0   0x00000004   Data   RW         1418    .data.SystemCoreClock  system_stm32f4xx.o
    0x20000014   0x08008fa4   0x00000020   Data   RW         1385    .data.USBH_msc      usbh_msc.o
    0x20000034   0x08008fc4   0x00000004   PAD
    0x20000038        -       0x00000005   Zero   RW          103    .bss..L_MergedGlobals  main.o
    0x2000003d   0x08008fc4   0x00000003   PAD
    0x20000040        -       0x0000002c   Zero   RW          337    .bss..L_MergedGlobals  ff.o
    0x2000006c        -       0x00000230   Zero   RW           99    .bss.MyFile         main.o
    0x2000029c        -       0x00000230   Zero   RW           98    .bss.USBDISKFatFs   main.o
    0x200004cc        -       0x00000010   Zero   RW          247    .bss.disk           ff_gen_drv.o
    0x200004dc        -       0x000004d8   Zero   RW           96    .bss.hUSBHost       main.o
    0x200009b4        -       0x000003e0   Zero   RW           75    .bss.hhcd           usbh_conf.o
    0x20000d94        -       0x00000048   Zero   RW           97    .bss.huart1         main.o
    0x20000ddc        -       0x00000200   Zero   RW          357    .bss.scratch        usbh_diskio_dma.o
    0x20000fdc        -       0x00000004   Zero   RW          579    .bss.uwTick         stm32f4xx_hal.o
    0x20000fe0        -       0x00000400   Zero   RW          112    HEAP                startup_stm32f429xx.o
    0x200013e0        -       0x00000540   Zero   RW          111    STACK               startup_stm32f429xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       162          0          0          0          0       2011   diskio.o
      7926        376        128          0         44      67707   ff.o
       124          0          0          0         16       3319   ff_gen_drv.o
      1870        256        923          0       2437      14154   main.o
        36          8        428          0       2368        836   startup_stm32f429xx.o
       206          0          0          8          4       7706   stm32f4xx_hal.o
       196          0          0          0          0      10886   stm32f4xx_hal_cortex.o
       424          0          0          0          0       5478   stm32f4xx_hal_gpio.o
      3016         38          0          0          0      15621   stm32f4xx_hal_hcd.o
      1484          4          0          0          0       7677   stm32f4xx_hal_rcc.o
       730          0          0          0          0      32799   stm32f4xx_hal_uart.o
        32          0          0          0          0       4167   stm32f4xx_it.o
      6888         26          0          0          0      37521   stm32f4xx_ll_usb.o
        86          0         24          4          0       2770   system_stm32f4xx.o
       522          0          0          0        992      16461   usbh_conf.o
      1754         40          0          0          0      12402   usbh_core.o
      1616         32          0          0          0      14787   usbh_ctlreq.o
       550          6         20          0        512       8627   usbh_diskio_dma.o
       212          0          0          0          0       8195   usbh_ioreq.o
      1482         10          4         32          0      13920   usbh_msc.o
       628         12          0          0          0      11371   usbh_msc_bot.o
       780          0          0          0          0      11948   usbh_msc_scsi.o
       270          0          0          0          0       6810   usbh_pipes.o

    ----------------------------------------------------------------------
     31180        <USER>       <GROUP>         44       6380     317173   Object Totals
         0          0         32          0          0          0   (incl. Generated)
       186          0          1          0          7          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        28          0          0          0          0         76   calloc.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       188         20          0          0          0        160   malloc.o
        36          0          0          0          0         68   memcpya.o
        36          0          0          0          0        108   memseta.o
         0          0          0          8          0          0   mvars.o
      2300         86          0          0          0        516   printfa.o
        14          0          0          0          0         68   strlen.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
       228          0          0          0          0         96   dmul.o

    ----------------------------------------------------------------------
      4012        <USER>          <GROUP>          8          0       2096   Library Totals
         6          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2940        124          0          8          0       1440   mc_w.l
      1066          0          0          0          0        656   mf_w.l

    ----------------------------------------------------------------------
      4012        <USER>          <GROUP>          8          0       2096   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     35192        932       1560         52       6380     317961   Grand Totals
     35192        932       1560         52       6380     317961   ELF Image Totals
     35192        932       1560         52          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                36752 (  35.89kB)
    Total RW  Size (RW Data + ZI Data)              6432 (   6.28kB)
    Total ROM Size (Code + RO Data + RW Data)      36804 (  35.94kB)

==============================================================================

