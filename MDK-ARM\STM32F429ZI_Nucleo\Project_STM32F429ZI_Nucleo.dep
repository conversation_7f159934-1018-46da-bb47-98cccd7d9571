Dependencies for Project 'Project', Target 'STM32F429ZI_Nucleo': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (../Src/stm32f4xx_it.c)(0x681F14B3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_it.o -MMD)
I (..\Inc\main.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Drivers\BSP\STM32F4xx_Nucleo_144\stm32f4xx_nucleo_144.h)(0x681F147B)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\Inc\ffconf.h)(0x681F14B3)
I (..\Inc\usbh_diskio_dma.h)(0x681F14B3)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
I (..\Inc\stm32f4xx_it.h)(0x681F14B3)
F (../Src/usbh_conf.c)(0x681F14B3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/usbh_conf.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
F (../Src/main.c)(0x681F14B3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/main.o -MMD)
I (..\Inc\main.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Drivers\BSP\STM32F4xx_Nucleo_144\stm32f4xx_nucleo_144.h)(0x681F147B)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\Inc\ffconf.h)(0x681F14B3)
I (..\Inc\usbh_diskio_dma.h)(0x681F14B3)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
F (startup_stm32f429xx.s)(0x681F14B3)(--cpu Cortex-M4.fp.sp -g --pd "__MICROLIB SETA 1" --diag_suppress=A1950W

-I.\RTE\_STM32F429ZI_Nucleo

-ID:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include

--pd "__UVISION_VERSION SETA 542"

--pd "STM32F429xx SETA 1"

--pd "_RTE_ SETA 1"

--list startup_stm32f429xx.lst

--xref -o stm32f429zi_nucleo\startup_stm32f429xx.o

--depend stm32f429zi_nucleo\startup_stm32f429xx.d)
F (../readme.txt)(0x681F14B3)()
F (../Drivers/BSP/STM32F4xx_Nucleo_144/stm32f4xx_nucleo_144.c)(0x681F147B)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_nucleo_144.o -MMD)
I (..\Drivers\BSP\STM32F4xx_Nucleo_144\stm32f4xx_nucleo_144.h)(0x681F147B)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_pipes.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/usbh_pipes.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ioreq.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/usbh_ioreq.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_core.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/usbh_core.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Core/Src/usbh_ctlreq.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/usbh_ctlreq.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
F (../Middlewares/Third_Party/FatFs/src/ff_gen_drv.c)(0x681F147E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/ff_gen_drv.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\Inc\ffconf.h)(0x681F14B3)
F (../Middlewares/Third_Party/FatFs/src/diskio.c)(0x681F147E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/diskio.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\Inc\ffconf.h)(0x681F14B3)
F (../Middlewares/Third_Party/FatFs/src/ff.c)(0x681F147E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/ff.o -MMD)
I (../Middlewares/Third_Party/FatFs/src/ff.h)(0x681F147E)
I (../Middlewares/Third_Party/FatFs/src/integer.h)(0x681F147E)
I (../Inc/ffconf.h)(0x681F14B3)
I (../Middlewares/Third_Party/FatFs/src/diskio.h)(0x681F147E)
F (../Src/usbh_diskio_dma.c)(0x681F14B3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/usbh_diskio_dma.o -MMD)
I (..\Middlewares\Third_Party\FatFs\src\ff_gen_drv.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\diskio.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\integer.h)(0x681F147E)
I (..\Middlewares\Third_Party\FatFs\src\ff.h)(0x681F147E)
I (..\Inc\ffconf.h)(0x681F14B3)
I (..\Inc\usbh_diskio_dma.h)(0x681F14B3)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_usb.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_ll_usb.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_i2c_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_dma_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_rcc_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_sdmmc.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_ll_sdmmc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_rcc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_pwr_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_uart.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sai_ex.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_sai_ex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_gpio.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_spi.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_spi.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_dma.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_i2c.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sdram.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_sdram.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_sai.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_sai.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_ll_fmc.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_ll_fmc.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_hcd.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_hcd.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_cortex.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c)(0x681F14AD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/stm32f4xx_hal_pwr.o -MMD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
F (../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_bot.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/usbh_msc_bot.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/usbh_msc.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
F (../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Src/usbh_msc_scsi.c)(0x681F14B9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/usbh_msc_scsi.o -MMD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_core.h)(0x681F14B9)
I (..\Inc\usbh_conf.h)(0x681F14B3)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_def.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ioreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_pipes.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Core\Inc\usbh_ctlreq.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_bot.h)(0x681F14B9)
I (..\Middlewares\ST\STM32_USB_Host_Library\Class\MSC\Inc\usbh_msc_scsi.h)(0x681F14B9)
F (../Src/system_stm32f4xx.c)(0x681F14B3)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m4 -mfpu=fpv4-sp-d16 -mfloat-abi=hard -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-D__MICROLIB -gdwarf-4 -O3 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -I ../Inc -I ../Drivers/CMSIS/Device/ST/STM32F4xx/Include -I ../Drivers/STM32F4xx_HAL_Driver/Inc -I ../Drivers/BSP/STM32F4xx_Nucleo_144 -I ../Drivers/BSP/Components/Common -I ../Middlewares/ST/STM32_USB_Host_Library/Core/Inc -I ../Middlewares/ST/STM32_USB_Host_Library/Class/MSC/Inc -I ../Middlewares/Third_Party/FatFs/src

-I./RTE/_STM32F429ZI_Nucleo

-ID:/Keil/ARM/CMSIS/6.1.0/CMSIS/Core/Include

-D__UVISION_VERSION="542" -DSTM32F429xx -D_RTE_ -DSTM32F429xx -DUSE_HAL_DRIVER -DUSE_STM32F4XX_NUCLEO_144

-o stm32f429zi_nucleo/system_stm32f4xx.o -MMD)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f4xx.h)(0x681F14AB)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\stm32f429xx.h)(0x681F14AB)
I (D:\Keil\ARM\CMSIS\6.1.0\CMSIS\Core\Include\core_cm4.h)(0x664BD888)
I (..\Drivers\CMSIS\Device\ST\STM32F4xx\Include\system_stm32f4xx.h)(0x681F14AB)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal.h)(0x681F14AD)
I (..\Inc\stm32f4xx_hal_conf.h)(0x681F14B3)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_def.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\Legacy\stm32_hal_legacy.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_rcc_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_gpio_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_dma_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_cortex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_flash_ramfunc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_fmc.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sdram.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_i2c_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_pwr_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_sai_ex.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_hal_hcd.h)(0x681F14AD)
I (..\Drivers\STM32F4xx_HAL_Driver\Inc\stm32f4xx_ll_usb.h)(0x681F14AD)
