/**
  ******************************************************************************
  * @file    sdram_diskio_template.h
  * <AUTHOR> Application Team
  * @brief   Header for sdram_diskio.c module.This filed needs to be customized
             and copied under the application project.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2017 STMicroelectronics. All rights reserved.
  *
  * This software component is licensed by ST under BSD 3-Clause license,
  * the "License"; You may not use this file except in compliance with the
  * License. You may obtain a copy of the License at:
  *                       opensource.org/licenses/BSD-3-Clause
  *
  ******************************************************************************
**/
#ifndef __SDRAM_DISKIO_H
#define __SDRAM_DISKIO_H

/* Includes ------------------------------------------------------------------*/
#include "stm32xxxx_{discovery}{eval}_sdram.h"
/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
extern const Diskio_drvTypeDef  SDRAMDISK_Driver;

#endif /* __SDRAM_DISKIO_H */

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/

