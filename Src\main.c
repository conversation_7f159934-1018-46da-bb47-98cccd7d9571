/**
  ******************************************************************************
  * @file    FatFs/FatFs_USBDisk/Src/main.c 
  * <AUTHOR> Application Team
  * @brief   Main program body
  *          This sample code shows how to use FatFs with USB disk drive.
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2017 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include <stdio.h>
#include <stdarg.h>
#include <string.h>

/* Private typedef -----------------------------------------------------------*/
/* Private define ------------------------------------------------------------*/
/* Private macro -------------------------------------------------------------*/
/* Private variables ---------------------------------------------------------*/
FATFS USBDISKFatFs;           /* File system object for USB disk logical drive */
FIL MyFile;                   /* File object */
char USBDISKPath[4];          /* USB Host logical drive path */
USBH_HandleTypeDef hUSBHost; /* USB Host handle */
UART_HandleTypeDef huart1;    /* UART handle for debug */

typedef enum {
  APPLICATION_IDLE = 0,  
  APPLICATION_START,    
  APPLICATION_RUNNING,
}MSC_ApplicationTypeDef;

MSC_ApplicationTypeDef Appli_state = APPLICATION_IDLE;

/* Private function prototypes -----------------------------------------------*/
static void SystemClock_Config(void);
static void Error_Handler(void);
static void USBH_UserProcess(USBH_HandleTypeDef *phost, uint8_t id);
static void MSC_Application(void);
void Custom_LED_Init(void);
void Debug_UART_Init(void);
void Debug_Printf(const char* format, ...);
void Custom_LED_Init(void);

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  Main program
  * @param  None
  * @retval None
  */
int main(void)
{
  /* STM32F4xx HAL library initialization:
       - Configure the Flash prefetch, instruction and Data caches
       - Configure the Systick to generate an interrupt each 1 msec
       - Set NVIC Group Priority to 4
       - Global MSP (MCU Support Package) initialization
     */
  HAL_Init();
  
  /* Configure the system clock to 180 MHz */
  SystemClock_Config();
  
  /* Configure LEDs */
  Custom_LED_Init();

  /* Initialize Debug UART */
  Debug_UART_Init();

  /* Print startup message */
  Debug_Printf("\r\n=== STM32F429IGT6 USB Host FatFs Demo ===\r\n");
  Debug_Printf("System Clock: 180MHz\r\n");
  Debug_Printf("USB Interface: OTG-HS\r\n");
  Debug_Printf("Waiting for USB device...\r\n");
  
  /*##-1- Link the USB Host disk I/O driver ##################################*/
  if(FATFS_LinkDriver(&USBH_Driver, USBDISKPath) == 0)
  {
    /*##-2- Init Host Library ################################################*/
    USBH_Init(&hUSBHost, USBH_UserProcess, 0);
    
    /*##-3- Add Supported Class ##############################################*/
    USBH_RegisterClass(&hUSBHost, USBH_MSC_CLASS);
    
    /*##-4- Start Host Process ###############################################*/
    USBH_Start(&hUSBHost);
    
    /*##-5- Run Application (Blocking mode) ##################################*/
    while (1)
    {
      /* USB Host Background task */
      USBH_Process(&hUSBHost);
      
      /* Mass Storage Application State Machine */
      switch(Appli_state)
      {
      case APPLICATION_START:
        MSC_Application();
        Appli_state = APPLICATION_IDLE;
        break;
         
      case APPLICATION_IDLE:
      default:
        break;      
      }
    }
  }
  
  /* Infinite loop */
  while (1)
  {
  }
    
}

/**
  * @brief  Main routine for Mass Storage Class
  * @param  None
  * @retval None
  */
static void MSC_Application(void)
{
  FRESULT res;                                          /* FatFs function common result code */
  uint32_t byteswritten, bytesread;                     /* File write/read counts */
  uint8_t wtext[] = "This is STM32F429IGT6 working with FatFs and USB-OTG-HS"; /* File write buffer */
  uint8_t rtext[100];                                   /* File read buffer */

  Debug_Printf("\r\n--- Starting USB Mass Storage Application ---\r\n");

  /* Register the file system object to the FatFs module */
  Debug_Printf("Mounting file system...\r\n");
  if(f_mount(&USBDISKFatFs, (TCHAR const*)USBDISKPath, 0) != FR_OK)
  {
    /* FatFs Initialization Error */
    Debug_Printf("ERROR: Failed to mount file system!\r\n");
    Error_Handler();
  }
  else
  {
    Debug_Printf("File system mounted successfully!\r\n");

    /* Create and Open a new text file object with write access */
    Debug_Printf("Creating file 'STM32.TXT'...\r\n");
    if(f_open(&MyFile, "STM32.TXT", FA_CREATE_ALWAYS | FA_WRITE) != FR_OK)
    {
      /* 'STM32.TXT' file Open for write Error */
      Debug_Printf("ERROR: Failed to create/open file for writing!\r\n");
      Error_Handler();
    }
    else
    {
      Debug_Printf("File created successfully!\r\n");

      /* Write data to the text file */
      Debug_Printf("Writing data to file...\r\n");
      res = f_write(&MyFile, wtext, sizeof(wtext), (void *)&byteswritten);

      if((byteswritten == 0) || (res != FR_OK))
      {
        /* 'STM32.TXT' file Write or EOF Error */
        Debug_Printf("ERROR: Failed to write data to file! (res=%d, bytes=%lu)\r\n", res, byteswritten);
        Error_Handler();
      }
      else
      {
        Debug_Printf("Data written successfully! (%lu bytes)\r\n", byteswritten);

        /* Close the open text file */
        f_close(&MyFile);
        Debug_Printf("File closed after writing.\r\n");

        /* Open the text file object with read access */
        Debug_Printf("Opening file for reading...\r\n");
        if(f_open(&MyFile, "STM32.TXT", FA_READ) != FR_OK)
        {
          /* 'STM32.TXT' file Open for read Error */
          Debug_Printf("ERROR: Failed to open file for reading!\r\n");
          Error_Handler();
        }
        else
        {
          Debug_Printf("File opened for reading successfully!\r\n");

          /* Read data from the text file */
          Debug_Printf("Reading data from file...\r\n");
          res = f_read(&MyFile, rtext, sizeof(rtext), (void *)&bytesread);

          if((bytesread == 0) || (res != FR_OK))
          {
            /* 'STM32.TXT' file Read or EOF Error */
            Debug_Printf("ERROR: Failed to read data from file! (res=%d, bytes=%lu)\r\n", res, bytesread);
            Error_Handler();
          }
          else
          {
            Debug_Printf("Data read successfully! (%lu bytes)\r\n", bytesread);
            Debug_Printf("Read content: %s\r\n", rtext);

            /* Close the open text file */
            f_close(&MyFile);
            Debug_Printf("File closed after reading.\r\n");

            /* Compare read data with the expected data */
            if((bytesread != byteswritten))
            {
              /* Read data is different from the expected data */
              Debug_Printf("ERROR: Data verification failed! (written=%lu, read=%lu)\r\n", byteswritten, bytesread);
              Error_Handler();
            }
            else
            {
              /* Success of the demo: no error occurrence */
              Debug_Printf("SUCCESS: File operation completed successfully!\r\n");
              Debug_Printf("Data verification passed!\r\n");
              BSP_LED_On(LED1);
            }
          }
        }
      }
    }
  }
  
  /* Unlink the USB disk I/O driver */
  FATFS_UnLinkDriver(USBDISKPath);
}


/**
  * @brief  User Process
  * @param  phost: Host handle
  * @param  id: Host Library user message ID
  * @retval None
  */
static void USBH_UserProcess(USBH_HandleTypeDef *phost, uint8_t id)
{
  switch(id)
  {
  case HOST_USER_SELECT_CONFIGURATION:
    Debug_Printf("USB: Configuration selected\r\n");
    break;

  case HOST_USER_DISCONNECTION:
    Debug_Printf("USB: Device disconnected\r\n");
    Appli_state = APPLICATION_IDLE;
    BSP_LED_Off(LED1);
    BSP_LED_Off(LED3);
    f_mount(NULL, (TCHAR const*)"", 0);
    break;

  case HOST_USER_CLASS_ACTIVE:
    Debug_Printf("USB: Mass Storage Class active - Device ready!\r\n");
    Appli_state = APPLICATION_START;
    break;

  default:
    break;
  }
}

/**
  * @brief  System Clock Configuration
  *         The system Clock is configured as follow :
  *            System Clock source            = PLL (HSE)
  *            SYSCLK(Hz)                     = 180000000
  *            HCLK(Hz)                       = 180000000
  *            AHB Prescaler                  = 1
  *            APB1 Prescaler                 = 4
  *            APB2 Prescaler                 = 2
  *            HSE Frequency(Hz)              = 8000000
  *            PLL_M                          = 8
  *            PLL_N                          = 360
  *            PLL_P                          = 2
  *            PLL_Q                          = 7
  *            VDD(V)                         = 3.3
  *            Main regulator output voltage  = Scale1 mode
  *            Flash Latency(WS)              = 5
  * @param  None
  * @retval None
  */
static void SystemClock_Config(void)
{
  RCC_ClkInitTypeDef RCC_ClkInitStruct;
  RCC_OscInitTypeDef RCC_OscInitStruct;

  /* Enable Power Control clock */
  __HAL_RCC_PWR_CLK_ENABLE();

  /* The voltage scaling allows optimizing the power consumption when the device is 
     clocked below the maximum system frequency, to update the voltage scaling value 
     regarding system frequency refer to product datasheet.  */
  __HAL_PWR_VOLTAGESCALING_CONFIG(PWR_REGULATOR_VOLTAGE_SCALE1);

  /* Enable HSE Oscillator and activate PLL with HSE as source */
  RCC_OscInitStruct.OscillatorType = RCC_OSCILLATORTYPE_HSE;
  RCC_OscInitStruct.HSEState = RCC_HSE_ON;
  RCC_OscInitStruct.PLL.PLLState = RCC_PLL_ON;
  RCC_OscInitStruct.PLL.PLLSource = RCC_PLLSOURCE_HSE;
  RCC_OscInitStruct.PLL.PLLM = 8;
  RCC_OscInitStruct.PLL.PLLN = 360;
  RCC_OscInitStruct.PLL.PLLP = 2;
  RCC_OscInitStruct.PLL.PLLQ = 7;
  HAL_RCC_OscConfig (&RCC_OscInitStruct);
  
  /* Select PLL as system clock source and configure the HCLK, PCLK1 and PCLK2 
     clocks dividers */
  RCC_ClkInitStruct.ClockType = (RCC_CLOCKTYPE_SYSCLK | RCC_CLOCKTYPE_HCLK | RCC_CLOCKTYPE_PCLK1 | RCC_CLOCKTYPE_PCLK2);
  RCC_ClkInitStruct.SYSCLKSource = RCC_SYSCLKSOURCE_PLLCLK;
  RCC_ClkInitStruct.AHBCLKDivider = RCC_SYSCLK_DIV1;
  RCC_ClkInitStruct.APB1CLKDivider = RCC_HCLK_DIV4;  
  RCC_ClkInitStruct.APB2CLKDivider = RCC_HCLK_DIV2;  
  HAL_RCC_ClockConfig(&RCC_ClkInitStruct, FLASH_LATENCY_5);
}

/**
  * @brief  This function is executed in case of error occurrence.
  * @param  None
  * @retval None
  */
static void Error_Handler(void)
{
  /* Turn LED3 on */
  Debug_Printf("FATAL ERROR: Entering error handler!\r\n");
  BSP_LED_On(LED3);
  while(1)
  {
    HAL_Delay(1000); /* Add delay to prevent flooding debug output */
  }
}

#ifdef  USE_FULL_ASSERT
/**
  * @brief  Reports the name of the source file and the source line number
  *         where the assert_param error has occurred.
  * @param  file: pointer to the source file name
  * @param  line: assert_param error line source number
  * @retval None
  */
void assert_failed(uint8_t* file, uint32_t line)
{ 
  /* User can add his own implementation to report the file name and line number,
     ex: printf("Wrong parameters value: file %s on line %d\r\n", file, line) */

  /* Infinite loop */
  while (1)
  {
  }
}
#endif

/**
  * @brief  Initialize custom RGB LED
  * @param  None
  * @retval None
  */
void Custom_LED_Init(void)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* GPIO Ports Clock Enable */
  LED_RED_GPIO_CLK_ENABLE();
  LED_GREEN_GPIO_CLK_ENABLE();
  LED_BLUE_GPIO_CLK_ENABLE();

  /* Configure GPIO pins : LED_RED_PIN LED_GREEN_PIN LED_BLUE_PIN */
  GPIO_InitStruct.Pin = LED_RED_PIN;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(LED_RED_GPIO_PORT, &GPIO_InitStruct);

  GPIO_InitStruct.Pin = LED_GREEN_PIN;
  HAL_GPIO_Init(LED_GREEN_GPIO_PORT, &GPIO_InitStruct);

  GPIO_InitStruct.Pin = LED_BLUE_PIN;
  HAL_GPIO_Init(LED_BLUE_GPIO_PORT, &GPIO_InitStruct);

  /* Turn off all LEDs initially */
  LED_RED_OFF();
  LED_GREEN_OFF();
  LED_BLUE_OFF();
}

/**
  * @brief  Initialize Debug UART
  * @param  None
  * @retval None
  */
void Debug_UART_Init(void)
{
  GPIO_InitTypeDef GPIO_InitStruct = {0};

  /* Peripheral clock enable */
  DEBUG_UART_CLK_ENABLE();
  DEBUG_UART_TX_GPIO_CLK_ENABLE();
  DEBUG_UART_RX_GPIO_CLK_ENABLE();

  /* UART TX GPIO pin configuration */
  GPIO_InitStruct.Pin = DEBUG_UART_TX_PIN;
  GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
  GPIO_InitStruct.Pull = GPIO_PULLUP;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
  GPIO_InitStruct.Alternate = DEBUG_UART_TX_AF;
  HAL_GPIO_Init(DEBUG_UART_TX_GPIO_PORT, &GPIO_InitStruct);

  /* UART RX GPIO pin configuration */
  GPIO_InitStruct.Pin = DEBUG_UART_RX_PIN;
  GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
  GPIO_InitStruct.Pull = GPIO_PULLUP;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
  GPIO_InitStruct.Alternate = DEBUG_UART_RX_AF;
  HAL_GPIO_Init(DEBUG_UART_RX_GPIO_PORT, &GPIO_InitStruct);

  /* UART configuration */
  huart1.Instance = DEBUG_UART;
  huart1.Init.BaudRate = 115200;
  huart1.Init.WordLength = UART_WORDLENGTH_8B;
  huart1.Init.StopBits = UART_STOPBITS_1;
  huart1.Init.Parity = UART_PARITY_NONE;
  huart1.Init.Mode = UART_MODE_TX_RX;
  huart1.Init.HwFlowCtl = UART_HWCONTROL_NONE;
  huart1.Init.OverSampling = UART_OVERSAMPLING_16;

  if (HAL_UART_Init(&huart1) != HAL_OK)
  {
    /* Initialization Error */
    while(1);
  }
}

/**
  * @brief  Debug printf function
  * @param  format: printf format string
  * @retval None
  */
void Debug_Printf(const char* format, ...)
{
  char buffer[256];
  va_list args;
  va_start(args, format);
  vsnprintf(buffer, sizeof(buffer), format, args);
  va_end(args);

  HAL_UART_Transmit(&huart1, (uint8_t*)buffer, strlen(buffer), HAL_MAX_DELAY);
}
