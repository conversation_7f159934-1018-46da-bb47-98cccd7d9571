# 🔧 STM32F429IGT6 USB磁盘串口调试功能

## 📋 功能概述

已为您的STM32F429IGT6开发板添加了完整的串口调试功能，可以实时监控USB磁盘读写过程和系统状态。

## ⚙️ 硬件连接

### UART1 调试串口
- **TX引脚**: PA9 (连接到USB转串口模块的RX)
- **RX引脚**: PA10 (连接到USB转串口模块的TX)
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无

### 连接示意图
```
STM32F429IGT6    USB转串口模块    电脑
    PA9    -->      RX      -->   串口助手
    PA10   <--      TX      <--   串口助手
    GND    ---      GND     ---   GND
```

## 🖥️ 串口助手设置

1. **打开串口助手** (如：SecureCRT, Tera Term, PuTTY等)
2. **配置参数**：
   - 波特率：115200
   - 数据位：8
   - 停止位：1
   - 校验位：无
   - 流控：无

## 📊 调试信息输出

### 系统启动信息
```
=== STM32F429IGT6 USB Host FatFs Demo ===
System Clock: 180MHz
USB Interface: OTG-HS
Waiting for USB device...
```

### USB设备事件
```
USB: Configuration selected
USB: Mass Storage Class active - Device ready!
USB: Device disconnected
```

### 文件操作过程
```
--- Starting USB Mass Storage Application ---
Mounting file system...
File system mounted successfully!
Creating file 'STM32.TXT'...
File created successfully!
Writing data to file...
Data written successfully! (58 bytes)
File closed after writing.
Opening file for reading...
File opened for reading successfully!
Reading data from file...
Data read successfully! (58 bytes)
Read content: This is STM32F429IGT6 working with FatFs and USB-OTG-HS
File closed after reading.
SUCCESS: File operation completed successfully!
Data verification passed!
```

### 错误信息示例
```
ERROR: Failed to mount file system!
ERROR: Failed to create/open file for writing!
ERROR: Failed to write data to file! (res=1, bytes=0)
ERROR: Data verification failed! (written=58, read=0)
FATAL ERROR: Entering error handler!
```

## 🎯 使用步骤

1. **硬件准备**
   - 连接USB转串口模块到PA9/PA10
   - 连接USB设备到开发板USB-OTG-HS接口

2. **软件准备**
   - 编译并下载程序到开发板
   - 打开串口助手，设置正确参数

3. **测试流程**
   - 复位开发板，观察启动信息
   - 插入U盘，观察USB事件和文件操作过程
   - 检查LED状态：绿色=成功，红色=错误

## 🔍 故障排除

### 无串口输出
- 检查PA9/PA10连接是否正确
- 确认串口助手参数设置
- 检查USB转串口驱动是否安装

### USB设备无法识别
- 确认USB-OTG-HS接口连接
- 检查5V供电是否正常
- 尝试不同的U盘

### 文件操作失败
- 检查U盘格式（建议FAT32）
- 确认U盘容量不要太大
- 检查U盘是否有写保护

## 📈 调试技巧

1. **实时监控**: 串口输出可以实时显示系统状态
2. **错误定位**: 详细的错误信息帮助快速定位问题
3. **性能分析**: 可以观察文件读写的字节数和耗时
4. **状态跟踪**: USB设备的连接/断开状态一目了然

## 🚀 扩展功能

您可以根据需要添加更多调试信息：
- 在关键函数入口添加 `Debug_Printf("Function_Name: Entry\r\n");`
- 在变量赋值后添加 `Debug_Printf("Variable = %d\r\n", variable);`
- 在条件判断处添加状态输出

现在您可以通过串口实时监控整个USB磁盘读写过程了！🎉
